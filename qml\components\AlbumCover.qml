import QtQuick
import QtQuick.Controls
import QtQuick.Effects

Item {
    id: root
    width: 200
    height: 200

    // Propiedades
    property string imageSource: ""
    property real rotationValue: 0
    property real scaleValue: 1.0
    property real opacityValue: 1.0
    property bool isPlaying: false

    // Se<PERSON><PERSON>
    signal clicked()
    signal mouseEntered()
    signal mouseLeft()

    // Rectángulo de fondo (para cuando no hay imagen o está cargando)
    Rectangle {
        id: background
        anchors.fill: parent
        color: "#222222"
        radius: 14
        visible: albumImage.status !== Image.Ready
    }

    // Imagen de la carátula
    Image {
        id: albumImage
        anchors.fill: parent
        source: root.imageSource
        fillMode: Image.PreserveAspectCrop
        visible: false  // Se oculta porque se muestra a través del OpacityMask

        onStatusChanged: {
            if (status === Image.Ready) {
                loadingAnimation.stop()
            } else if (status === Image.Loading) {
                loadingAnimation.start()
            }
        }
    }

    // Máscara para bordes redondeados
    Rectangle {
        id: mask
        anchors.fill: parent
        radius: 14
        visible: false
    }

    // Aplicar máscara a la imagen
    Item {
        id: maskedAlbum
        anchors.fill: parent
        opacity: root.opacityValue

        // Imagen con máscara
        Image {
            id: maskedImage
            anchors.fill: parent
            source: albumImage.source
            fillMode: Image.PreserveAspectCrop
            visible: true

            layer.enabled: true
            layer.effect: MultiEffect {
                maskEnabled: true
                maskSource: mask
            }
        }

        // Transformaciones para animaciones
        transform: [
            Rotation {
                id: rotation
                origin.x: maskedAlbum.width/2
                origin.y: maskedAlbum.height/2
                angle: root.rotationValue
            },
            Scale {
                id: scale
                origin.x: maskedAlbum.width/2
                origin.y: maskedAlbum.height/2
                xScale: root.scaleValue
                yScale: root.scaleValue
            }
        ]
    }

    // Efecto de sombra
    Item {
        anchors.fill: maskedAlbum

        MultiEffect {
            anchors.fill: parent
            source: maskedAlbum
            shadowEnabled: true
            shadowHorizontalOffset: 3
            shadowVerticalOffset: 3
            shadowBlur: 8.0
            shadowColor: "#80000000"
        }
    }

    // Animación de carga
    Rectangle {
        id: loadingIndicator
        width: 40
        height: 40
        radius: 20
        color: "#333333"
        anchors.centerIn: parent
        visible: albumImage.status === Image.Loading

        Rectangle {
            width: 20
            height: 20
            radius: 10
            color: "#ffffff"
            anchors.centerIn: parent

            RotationAnimation {
                id: loadingAnimation
                target: loadingIndicator
                from: 0
                to: 360
                duration: 1500
                loops: Animation.Infinite
                running: false
            }
        }
    }

    // Animación de rotación para cuando está reproduciendo
    RotationAnimation {
        id: playingRotation
        target: maskedAlbum
        from: 0
        to: 360
        duration: 20000  // 20 segundos para una rotación completa
        loops: Animation.Infinite
        running: root.isPlaying

        onRunningChanged: {
            if (!running) {
                // Resetear suavemente a 0 cuando se detiene
                var currentAngle = maskedAlbum.rotation
                resetAnimation.from = currentAngle
                resetAnimation.start()
            }
        }
    }

    // Animación para resetear la rotación
    NumberAnimation {
        id: resetAnimation
        target: maskedAlbum
        property: "rotation"
        from: 0  // Se actualizará dinámicamente
        to: 0
        duration: 500
        easing.type: Easing.OutQuad
    }

    // Animaciones de comportamiento para transiciones suaves
    Behavior on opacityValue {
        NumberAnimation {
            duration: 300
            easing.type: Easing.OutCubic
        }
    }

    Behavior on scaleValue {
        NumberAnimation {
            duration: 300
            easing.type: Easing.OutCubic
        }
    }

    // Área para detectar interacciones del mouse
    MouseArea {
        anchors.fill: parent
        hoverEnabled: true

        onClicked: root.clicked()
        onEntered: {
            root.mouseEntered()
            scaleAnimation.start()
        }
        onExited: {
            root.mouseLeft()
            scaleAnimation.stop()
            root.scaleValue = 1.0
        }
    }

    // Animación de escala al pasar el mouse
    SequentialAnimation {
        id: scaleAnimation
        loops: Animation.Infinite
        running: false

        NumberAnimation {
            target: root
            property: "scaleValue"
            from: 1.0
            to: 1.05
            duration: 700
            easing.type: Easing.OutCubic
        }
        NumberAnimation {
            target: root
            property: "scaleValue"
            from: 1.05
            to: 1.0
            duration: 700
            easing.type: Easing.InOutCubic
        }
    }

    // Función para animar un "pulso" (como un latido)
    function animatePulse() {
        pulseAnimation.start()
    }

    // Animación de pulso
    SequentialAnimation {
        id: pulseAnimation

        NumberAnimation {
            target: root
            property: "scaleValue"
            from: 1.0
            to: 1.18
            duration: 150
            easing.type: Easing.OutCubic
        }
        NumberAnimation {
            target: root
            property: "scaleValue"
            from: 1.18
            to: 0.98
            duration: 150
            easing.type: Easing.InOutCubic
        }
        NumberAnimation {
            target: root
            property: "scaleValue"
            from: 0.98
            to: 1.04
            duration: 120
            easing.type: Easing.OutCubic
        }
        NumberAnimation {
            target: root
            property: "scaleValue"
            from: 1.04
            to: 1.0
            duration: 100
            easing.type: Easing.InOutCubic
        }
    }
}
