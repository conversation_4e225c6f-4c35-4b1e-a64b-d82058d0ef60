import QtQuick
import QtQuick.Controls
import QtQuick.Layouts
import QtQuick.Effects

Rectangle {
    id: root
    width: 300
    height: parent.height
    color: "transparent"

    // Propiedades
    property var playlistModel: []
    property int currentIndex: -1
    property bool isShuffled: false
    property bool isLooped: false
    property bool showLibraries: false
    property string assetsPath: "file:qml/assets/"

    // Señales
    signal songSelected(int index)
    signal panelToggled(bool expanded)

    // Fondo con efecto mica/blur
    Rectangle {
        anchors.fill: parent
        color: Qt.rgba(15/255, 15/255, 15/255, 0.75)
        radius: 0

        // Efecto de sombra
        layer.enabled: true
        layer.effect: MultiEffect {
            shadowEnabled: true
            shadowHorizontalOffset: -3
            shadowVerticalOffset: 0
            shadowBlur: 20.0
            shadowColor: Qt.rgba(0, 0, 0, 0.8)
        }
    }

    // Contenido del panel
    ColumnLayout {
        anchors.fill: parent
        anchors.margins: 15
        spacing: 15

        // Título del panel con botón de cerrar
        RowLayout {
            Layout.fillWidth: true
            spacing: 10

            Label {
                id: titleLabel
                text: root.showLibraries ? "Bibliotecas" : "Lista de Reproducción"
                font.pixelSize: 18
                font.bold: true
                color: "#FFFFFF"
                Layout.fillWidth: true

                // Animación de cambio de texto
                Behavior on text {
                    SequentialAnimation {
                        NumberAnimation {
                            target: titleLabel
                            property: "opacity"
                            to: 0
                            duration: 150
                        }
                        PropertyAction {
                            target: titleLabel
                            property: "text"
                        }
                        NumberAnimation {
                            target: titleLabel
                            property: "opacity"
                            to: 1
                            duration: 150
                        }
                    }
                }
            }

            // Botón de cerrar
            Rectangle {
                width: 24
                height: 24
                radius: 12
                color: closeMouseArea.containsMouse ? Qt.rgba(255, 255, 255, 0.1) : "transparent"

                Image {
                    anchors.centerIn: parent
                    width: 16
                    height: 16
                    source: root.assetsPath + "close.svg"
                    sourceSize: Qt.size(16, 16)

                    layer.enabled: true
                    layer.effect: MultiEffect {
                        colorization: 1.0
                        colorizationColor: "#ffffff"
                    }
                }

                MouseArea {
                    id: closeMouseArea
                    anchors.fill: parent
                    hoverEnabled: true
                    cursorShape: Qt.PointingHandCursor

                    onClicked: {
                        root.panelToggled(false)
                    }
                }

                Behavior on color {
                    ColorAnimation { duration: 200 }
                }
            }
        }

        // Barra de búsqueda con botón de biblioteca
        RowLayout {
            Layout.fillWidth: true
            spacing: 10

            // Contenedor de búsqueda
            Rectangle {
                Layout.fillWidth: true
                height: 30
                radius: 15
                color: searchInput.activeFocus ? Qt.rgba(40/255, 40/255, 40/255, 0.7) : Qt.rgba(30/255, 30/255, 30/255, 0.6)
                border.color: searchInput.activeFocus ? Qt.rgba(100/255, 100/255, 100/255, 0.4) : Qt.rgba(80/255, 80/255, 80/255, 0.3)
                border.width: 1

                RowLayout {
                    anchors.fill: parent
                    anchors.margins: 10
                    spacing: 8

                    Image {
                        width: 16
                        height: 16
                        source: root.assetsPath + "search.svg"
                        sourceSize: Qt.size(16, 16)

                        layer.enabled: true
                        layer.effect: MultiEffect {
                            colorization: 1.0
                            colorizationColor: "#aaaaaa"
                        }
                    }

                    TextInput {
                        id: searchInput
                        Layout.fillWidth: true
                        color: "#ffffff"
                        font.pixelSize: 14
                        selectByMouse: true
                        selectionColor: "#555555"

                        Text {
                            anchors.fill: parent
                            text: "Buscar canción..."
                            color: "#aaaaaa"
                            font.pixelSize: 14
                            visible: !searchInput.text && !searchInput.activeFocus
                            verticalAlignment: Text.AlignVCenter
                        }
                    }
                }

                Behavior on color {
                    ColorAnimation { duration: 200 }
                }

                Behavior on border.color {
                    ColorAnimation { duration: 200 }
                }
            }

            // Botón de biblioteca
            Rectangle {
                width: 30
                height: 30
                radius: 15
                color: libraryMouseArea.containsMouse ? Qt.rgba(255, 255, 255, 0.1) : "transparent"

                Image {
                    id: libraryIcon
                    anchors.centerIn: parent
                    width: 16
                    height: 16
                    source: root.showLibraries ? root.assetsPath + "music.svg" : root.assetsPath + "library.svg"
                    sourceSize: Qt.size(16, 16)

                    layer.enabled: true
                    layer.effect: MultiEffect {
                        colorization: 1.0
                        colorizationColor: root.showLibraries ? "#1e90ff" : "#aaaaaa"
                    }

                    // Animación mejorada para el cambio de icono
                    Behavior on source {
                        SequentialAnimation {
                            NumberAnimation {
                                target: libraryIcon
                                property: "opacity"
                                to: 0
                                duration: 150
                                easing.type: Easing.OutQuad
                            }
                            PropertyAction {
                                target: libraryIcon
                                property: "source"
                            }
                            NumberAnimation {
                                target: libraryIcon
                                property: "opacity"
                                to: 1
                                duration: 150
                                easing.type: Easing.InQuad
                            }
                        }
                    }
                }

                MouseArea {
                    id: libraryMouseArea
                    anchors.fill: parent
                    hoverEnabled: true
                    cursorShape: Qt.PointingHandCursor

                    onClicked: {
                        // Iniciar la animación de desplazamiento horizontal
                        horizontalSlideAnimation.start()
                    }
                }

                Behavior on color {
                    ColorAnimation { duration: 200 }
                }
            }
        }

        // Separador
        Rectangle {
            Layout.fillWidth: true
            height: 1
            color: Qt.rgba(100/255, 100/255, 100/255, 0.3)
        }

        // Contenedor principal con clip para el desplazamiento horizontal
        Item {
            Layout.fillWidth: true
            Layout.fillHeight: true
            clip: true

            // Contenedor deslizante que contiene ambas vistas
            Item {
                id: slidingContainer
                width: parent.width * 2  // Doble ancho para contener ambas vistas
                height: parent.height
                x: root.showLibraries ? -parent.width : 0  // Posición basada en el estado

                // Animación suave para el desplazamiento horizontal
                Behavior on x {
                    NumberAnimation {
                        duration: 400
                        easing.type: Easing.OutCubic
                    }
                }

                // Vista de lista de reproducción (lado izquierdo)
                ScrollView {
                    id: songsScrollView
                    x: 0
                    width: root.width - 30  // Ancho del panel menos márgenes
                    height: parent.height
                    clip: true

                    ListView {
                        id: songsListView
                        spacing: 3
                        model: songsModel

                        // Modelo de canciones
                        property var songsModel: [
                            {"title": "Abyss", "artist": "YUNGBLUD", "duration": "2:03", "active": true},
                            {"title": "Blinding Lights", "artist": "The Weeknd", "duration": "3:20", "active": false},
                            {"title": "Heat Waves", "artist": "Glass Animals", "duration": "3:59", "active": false},
                            {"title": "As It Was", "artist": "Harry Styles", "duration": "2:47", "active": false},
                            {"title": "Levitating", "artist": "Dua Lipa", "duration": "3:23", "active": false},
                            {"title": "Stay", "artist": "The Kid LAROI, Justin Bieber", "duration": "2:21", "active": false},
                            {"title": "Bad Habits", "artist": "Ed Sheeran", "duration": "3:51", "active": false},
                            {"title": "good 4 u", "artist": "Olivia Rodrigo", "duration": "2:58", "active": false},
                            {"title": "Save Your Tears", "artist": "The Weeknd", "duration": "3:35", "active": false},
                            {"title": "Montero", "artist": "Lil Nas X", "duration": "2:17", "active": false},
                            {"title": "Peaches", "artist": "Justin Bieber", "duration": "3:18", "active": false},
                            {"title": "Kiss Me More", "artist": "Doja Cat ft. SZA", "duration": "3:28", "active": false},
                            {"title": "Shivers", "artist": "Ed Sheeran", "duration": "3:27", "active": false},
                            {"title": "Industry Baby", "artist": "Lil Nas X, Jack Harlow", "duration": "3:32", "active": false},
                            {"title": "Easy On Me", "artist": "Adele", "duration": "3:44", "active": false}
                        ]

                        delegate: playlistItemDelegate
                    }
                }
                
                // Vista de bibliotecas (lado derecho)
                ScrollView {
                    id: librariesScrollView
                    x: root.width - 30  // Posicionado a la derecha de la vista de canciones
                    width: root.width - 30  // Ancho del panel menos márgenes
                    height: parent.height
                    clip: true

                    ListView {
                        id: librariesListView
                        spacing: 3
                        model: librariesModel

                        // Modelo de bibliotecas
                        property var librariesModel: [
                            {"title": "Favoritos", "artist": "15 canciones", "duration": "", "active": false},
                            {"title": "Pop Hits", "artist": "32 canciones", "duration": "", "active": false},
                            {"title": "Rock Clásico", "artist": "28 canciones", "duration": "", "active": false},
                            {"title": "Electrónica", "artist": "45 canciones", "duration": "", "active": false},
                            {"title": "Hip Hop", "artist": "20 canciones", "duration": "", "active": false},
                            {"title": "Música Latina", "artist": "25 canciones", "duration": "", "active": false},
                            {"title": "Indie", "artist": "18 canciones", "duration": "", "active": false},
                            {"title": "Relajación", "artist": "12 canciones", "duration": "", "active": false},
                            {"title": "Workout", "artist": "30 canciones", "duration": "", "active": true},
                            {"title": "Fiesta", "artist": "40 canciones", "duration": "", "active": false}
                        ]

                        delegate: playlistItemDelegate
                    }
                }

                // Selector móvil animado (efecto jukebox)
                Rectangle {
                    id: movingSelector
                    width: root.width - 50  // Ancho del panel menos márgenes
                    height: 70  // Misma altura que los items
                    radius: 12
                    color: "transparent"
                    border.color: Qt.rgba(30/255, 144/255, 255/255, 0.8)
                    border.width: 2
                    visible: false
                    z: 5  // Asegurar que esté por encima de los items
                    
                    // Propiedades para controlar la animación
                    property int targetIndex: -1
                    property bool animating: false
                    property real targetY: 0
                    
                    // Gradiente de fondo para el selector
                    Rectangle {
                        anchors.fill: parent
                        anchors.margins: 1
                        radius: parent.radius - 1
                        gradient: Gradient {
                            GradientStop { 
                                position: 0.0
                                color: Qt.rgba(30/255, 144/255, 255/255, 0.15)
                            }
                            GradientStop { 
                                position: 0.5
                                color: Qt.rgba(30/255, 144/255, 255/255, 0.08)
                            }
                            GradientStop { 
                                position: 1.0
                                color: Qt.rgba(30/255, 144/255, 255/255, 0.15)
                            }
                        }
                    }
                    
                    // Efecto de brillo en los bordes
                    Rectangle {
                        anchors.fill: parent
                        radius: parent.radius
                        color: "transparent"
                        border.color: Qt.rgba(255/255, 255/255, 255/255, 0.3)
                        border.width: 1
                    }
                    
                    // Animación de movimiento suave
                    Behavior on y {
                        enabled: movingSelector.animating
                        SequentialAnimation {
                            // Fase 1: Aceleración inicial
                            NumberAnimation {
                                duration: 200
                                easing.type: Easing.OutCubic
                            }
                            
                            // Callback al finalizar el movimiento
                            ScriptAction {
                                script: {
                                    movingSelector.animating = false
                                    // Iniciar animación de confirmación
                                    selectorConfirmAnimation.start()
                                }
                            }
                        }
                    }
                    
                    // Animación de aparición inicial
                    SequentialAnimation {
                        id: selectorAppearAnimation
                        
                        PropertyAction {
                            target: movingSelector
                            property: "visible"
                            value: true
                        }
                        
                        PropertyAction {
                            target: movingSelector
                            property: "scale"
                            value: 0.8
                        }
                        
                        PropertyAction {
                            target: movingSelector
                            property: "opacity"
                            value: 0.0
                        }
                        
                        ParallelAnimation {
                            NumberAnimation {
                                target: movingSelector
                                property: "scale"
                                from: 0.8
                                to: 1.0
                                duration: 300
                                easing.type: Easing.OutBack
                            }
                            
                            NumberAnimation {
                                target: movingSelector
                                property: "opacity"
                                from: 0.0
                                to: 1.0
                                duration: 250
                                easing.type: Easing.OutCubic
                            }
                        }
                    }
                    
                    // Animación de confirmación después del movimiento
                    SequentialAnimation {
                        id: selectorConfirmAnimation
                        
                        // Pulso de confirmación
                        NumberAnimation {
                            target: movingSelector
                            property: "scale"
                            from: 1.0
                            to: 1.05
                            duration: 150
                            easing.type: Easing.OutCubic
                        }
                        
                        NumberAnimation {
                            target: movingSelector
                            property: "scale"
                            from: 1.05
                            to: 1.0
                            duration: 200
                            easing.type: Easing.OutBounce
                        }
                        
                        // Brillo temporal en el borde
                        ParallelAnimation {
                            NumberAnimation {
                                target: movingSelector
                                property: "border.width"
                                from: 2
                                to: 3
                                duration: 100
                            }
                            
                            ColorAnimation {
                                target: movingSelector
                                property: "border.color"
                                from: Qt.rgba(30/255, 144/255, 255/255, 0.8)
                                to: Qt.rgba(30/255, 144/255, 255/255, 1.0)
                                duration: 100
                            }
                        }
                        
                        ParallelAnimation {
                            NumberAnimation {
                                target: movingSelector
                                property: "border.width"
                                from: 3
                                to: 2
                                duration: 200
                            }
                            
                            ColorAnimation {
                                target: movingSelector
                                property: "border.color"
                                from: Qt.rgba(30/255, 144/255, 255/255, 1.0)
                                to: Qt.rgba(30/255, 144/255, 255/255, 0.8)
                                duration: 200
                            }
                        }
                    }
                    
                    // Función para mover el selector a un índice específico
                    function moveToIndex(index, listView) {
                        if (index < 0 || !listView) return
                        
                        // Calcular la posición Y del item objetivo
                        var itemY = index * (70 + listView.spacing)  // altura del item + spacing
                        
                        // Ajustar por la posición del header si existe
                        if (listView.headerItem) {
                            itemY += listView.headerItem.height
                        }
                        
                        // Ajustar por el contentY actual de la lista
                        var relativeY = itemY - listView.contentY
                        
                        // Asegurar que el selector esté dentro de los límites visibles
                        var maxY = listView.height - movingSelector.height
                        var minY = 0
                        
                        targetY = Math.max(minY, Math.min(maxY, relativeY))
                        
                        // Si es la primera vez, aparecer en la posición sin animación
                        if (!visible) {
                            y = targetY
                            selectorAppearAnimation.start()
                        } else {
                            // Animar el movimiento
                            animating = true
                            y = targetY
                        }
                    }
                }
            }
        }

        // Componente para cada elemento de la playlist
    Component {
        id: playlistItemDelegate
        Rectangle {
            id: itemRoot
            width: ListView.view.width
            height: 70
            color: "transparent"
            
            property string title: modelData.title
            property string artist: modelData.artist
            property string duration: modelData.duration
            property bool isActive: modelData.active
            property bool isHovered: false
            
            // Propiedades para el efecto de luz
            property bool lightEffectActive: false
            property real lightEffectProgress: 0.0
            property point lightEffectCenter: Qt.point(0, 0)
            
            // Propiedades para la animación de selección
            property bool wasActive: false
            property bool animatingSelection: false

            // Detectar cambios en el estado activo para iniciar animaciones
            onIsActiveChanged: {
                // Si el elemento se activa, iniciar la animación de activación
                if (isActive && !wasActive) {
                    wasActive = true
                    animatingSelection = true
                    selectionAnimation.start()
                }
                // Si el elemento se desactiva, iniciar la animación de desactivación
                else if (!isActive && wasActive) {
                    wasActive = false
                    animatingSelection = true
                    deselectionAnimation.start()
                }
            }

            // Indicador lateral para elementos activos
            Rectangle {
                id: activeIndicator
                anchors.left: parent.left
                anchors.top: parent.top
                anchors.bottom: parent.bottom
                width: itemRoot.isActive ? 4 : 0
                radius: 2
                color: Qt.rgba(30/255, 144/255, 255/255, 0.9)
                
                // Animación suave para la aparición/desaparición
                Behavior on width {
                    NumberAnimation {
                        duration: 300
                        easing.type: Easing.OutCubic
                    }
                }
                
                // Efecto de brillo para el indicador activo
                layer.enabled: itemRoot.isActive
                layer.effect: MultiEffect {
                    shadowEnabled: true
                    shadowHorizontalOffset: 0
                    shadowVerticalOffset: 0
                    shadowColor: Qt.rgba(30/255, 144/255, 255/255, 0.7)
                    shadowBlur: 12
                }
            }

            // Fondo con efecto mica
            Rectangle {
                id: itemBackground
                anchors.fill: parent
                anchors.leftMargin: activeIndicator.width > 0 ? 2 : 0
                anchors.rightMargin: 2
                anchors.topMargin: 2
                anchors.bottomMargin: 2
                radius: 10
                color: {
                    if (itemRoot.isActive) {
                        return Qt.rgba(40/255, 40/255, 40/255, 0.25) // Reducido de 0.47 a 0.25
                    } else if (itemRoot.isHovered) {
                        return Qt.rgba(20/255, 60/255, 120/255, 0.47) // Fondo azul para hover
                    } else {
                        return Qt.rgba(30/255, 30/255, 30/255, 0.31) // Fondo sutil para elementos normales
                    }
                }
                
                border.color: {
                    if (itemRoot.isActive) {
                        return Qt.rgba(255/255, 255/255, 255/255, 0.24)
                    } else if (itemRoot.isHovered) {
                        return Qt.rgba(30/255, 144/255, 255/255, 0.31)
                    } else {
                        return Qt.rgba(255/255, 255/255, 255/255, 0.08)
                    }
                }
                border.width: 1

                // Animaciones suaves para los cambios de estado
                Behavior on color {
                    ColorAnimation { duration: 300 }
                }

                Behavior on border.color {
                    ColorAnimation { duration: 300 }
                }
                
                Behavior on anchors.leftMargin {
                    NumberAnimation { duration: 300; easing.type: Easing.OutCubic }
                }

                // Gradiente superior para efecto mica
                Rectangle {
                    anchors.fill: parent
                    radius: parent.radius
                    gradient: Gradient {
                        GradientStop { 
                            position: 0.0
                            color: {
                                if (itemRoot.isActive) {
                                    return Qt.rgba(255/255, 255/255, 255/255, 0.16)
                                } else if (itemRoot.isHovered) {
                                    return Qt.rgba(30/255, 144/255, 255/255, 0.16)
                                } else {
                                    return Qt.rgba(255/255, 255/255, 255/255, 0.06)
                                }
                            }
                        }
                        GradientStop { 
                            position: 1.0
                            color: {
                                if (itemRoot.isActive) {
                                    return Qt.rgba(255/255, 255/255, 255/255, 0.02)
                                } else if (itemRoot.isHovered) {
                                    return Qt.rgba(30/255, 144/255, 255/255, 0.04)
                                } else {
                                    return Qt.rgba(255/255, 255/255, 255/255, 0.0)
                                }
                            }
                        }
                    }
                }
            }

            // Contenido del elemento
            RowLayout {
                id: contentLayout
                anchors.fill: parent
                anchors.leftMargin: 15 + activeIndicator.width
                anchors.rightMargin: 15
                anchors.topMargin: 15
                anchors.bottomMargin: 15
                spacing: 15
                
                // Animación suave para el margen izquierdo
                Behavior on anchors.leftMargin {
                    NumberAnimation { duration: 300; easing.type: Easing.OutCubic }
                }

                // Carátula del álbum (placeholder)
                Rectangle {
                    id: albumCover
                    width: 40
                    height: 40
                    radius: 5
                    color: Qt.rgba(100/255, 100/255, 100/255, 1.0)
                    Layout.alignment: Qt.AlignVCenter
                    
                    // Efecto de brillo para elementos activos
                    layer.enabled: itemRoot.isActive
                    layer.effect: MultiEffect {
                        shadowEnabled: true
                        shadowHorizontalOffset: 0
                        shadowVerticalOffset: 0
                        shadowColor: Qt.rgba(30/255, 144/255, 255/255, 0.5)
                        shadowBlur: 8
                    }
                    
                    // Animación de escala para elementos activos
                    transform: Scale {
                        origin.x: albumCover.width / 2
                        origin.y: albumCover.height / 2
                        xScale: itemRoot.isActive ? 1.05 : 1.0
                        yScale: itemRoot.isActive ? 1.05 : 1.0
                        
                        Behavior on xScale {
                            NumberAnimation { duration: 300; easing.type: Easing.OutCubic }
                        }
                        
                        Behavior on yScale {
                            NumberAnimation { duration: 300; easing.type: Easing.OutCubic }
                        }
                    }
                }

                // Icono de reproducción (si está activo)
                Image {
                    id: playingIcon
                    width: 20
                    height: 20
                    source: itemRoot.isActive ? root.assetsPath + "playing.svg" : ""
                    sourceSize: Qt.size(20, 20)
                    visible: opacity > 0
                    opacity: itemRoot.isActive ? 1.0 : 0.0
                    Layout.alignment: Qt.AlignVCenter

                    layer.enabled: true
                    layer.effect: MultiEffect {
                        colorization: 1.0
                        colorizationColor: "#ffffff"
                    }
                    
                    // Animación suave para aparecer/desaparecer
                    Behavior on opacity {
                        NumberAnimation { 
                            duration: 400
                            easing.type: Easing.OutCubic
                        }
                    }
                    
                    // Animación de entrada con rebote
                    SequentialAnimation {
                        id: playingIconAnimation
                        running: itemRoot.isActive && itemRoot.animatingSelection
                        
                        NumberAnimation {
                            target: playingIcon
                            property: "scale"
                            from: 0.5
                            to: 1.2
                            duration: 200
                            easing.type: Easing.OutCubic
                        }
                        
                        NumberAnimation {
                            target: playingIcon
                            property: "scale"
                            from: 1.2
                            to: 1.0
                            duration: 150
                            easing.type: Easing.OutBounce
                        }
                    }
                }

                // Información de la canción
                ColumnLayout {
                    Layout.fillWidth: true
                    Layout.alignment: Qt.AlignVCenter
                    spacing: 2

                    Text {
                        id: titleText
                        text: itemRoot.title
                        font.pixelSize: 11
                        font.bold: true
                        color: itemRoot.isActive ? "#ffffff" : "#dddddd"
                        elide: Text.ElideRight
                        Layout.fillWidth: true
                        
                        // Animación suave para el cambio de color
                        Behavior on color {
                            ColorAnimation { duration: 300 }
                        }
                        
                        // Animación de escala para texto activo
                        transform: Scale {
                            origin.x: 0
                            origin.y: titleText.height / 2
                            xScale: itemRoot.isActive ? 1.05 : 1.0
                            yScale: itemRoot.isActive ? 1.05 : 1.0
                            
                            Behavior on xScale {
                                NumberAnimation { duration: 300; easing.type: Easing.OutCubic }
                            }
                            
                            Behavior on yScale {
                                NumberAnimation { duration: 300; easing.type: Easing.OutCubic }
                            }
                        }
                    }

                    Text {
                        id: artistText
                        text: itemRoot.artist
                        font.pixelSize: 10
                        color: itemRoot.isActive ? "#cccccc" : "#aaaaaa"
                        elide: Text.ElideRight
                        Layout.fillWidth: true
                        
                        // Animación suave para el cambio de color
                        Behavior on color {
                            ColorAnimation { duration: 300 }
                        }
                    }
                }

                // Duración
                Text {
                    id: durationText
                    text: itemRoot.duration
                    font.pixelSize: 10
                    color: itemRoot.isActive ? "#cccccc" : "#aaaaaa"
                    Layout.alignment: Qt.AlignVCenter
                    visible: itemRoot.duration.length > 0
                    
                    // Animación suave para el cambio de color
                    Behavior on color {
                        ColorAnimation { duration: 300 }
                    }
                }
            }

            // Efecto de punto de luz
            Rectangle {
                anchors.fill: parent
                anchors.margins: 2
                radius: 10
                visible: itemRoot.lightEffectActive && itemRoot.lightEffectProgress > 0
                clip: true
                color: "transparent"

                // Círculo de luz usando múltiples capas para simular gradiente radial
                Item {
                    x: itemRoot.lightEffectCenter.x - currentRadius
                    y: itemRoot.lightEffectCenter.y - currentRadius
                    width: currentRadius * 2
                    height: currentRadius * 2
                    
                    property real currentRadius: {
                        var maxRadius = Math.max(itemRoot.width, itemRoot.height) * 3.5
                        return maxRadius * itemRoot.lightEffectProgress
                    }
                    
                    // Capa externa (más transparente)
                    Rectangle {
                        anchors.centerIn: parent
                        width: parent.currentRadius * 2
                        height: parent.currentRadius * 2
                        radius: width / 2
                        color: Qt.rgba(20/255, 80/255, 200/255, 0.1 * (1.0 - itemRoot.lightEffectProgress * 0.5))
                    }
                    
                    // Capa media
                    Rectangle {
                        anchors.centerIn: parent
                        width: parent.currentRadius * 1.4
                        height: parent.currentRadius * 1.4
                        radius: width / 2
                        color: Qt.rgba(40/255, 120/255, 230/255, 0.2 * (1.0 - itemRoot.lightEffectProgress * 0.5))
                    }
                    
                    // Capa media-interior
                    Rectangle {
                        anchors.centerIn: parent
                        width: parent.currentRadius * 0.8
                        height: parent.currentRadius * 0.8
                        radius: width / 2
                        color: Qt.rgba(80/255, 160/255, 255/255, 0.4 * (1.0 - itemRoot.lightEffectProgress * 0.5))
                    }
                    
                    // Capa interior
                    Rectangle {
                        anchors.centerIn: parent
                        width: parent.currentRadius * 0.3
                        height: parent.currentRadius * 0.3
                        radius: width / 2
                        color: Qt.rgba(150/255, 200/255, 255/255, 0.6 * (1.0 - itemRoot.lightEffectProgress * 0.5))
                    }
                    
                    // Centro brillante
                    Rectangle {
                        anchors.centerIn: parent
                        width: parent.currentRadius * 0.1
                        height: parent.currentRadius * 0.1
                        radius: width / 2
                        color: Qt.rgba(255/255, 255/255, 255/255, 0.8 * (1.0 - itemRoot.lightEffectProgress * 0.5))
                    }
                }
            }

            // Área de interacción
            MouseArea {
                anchors.fill: parent
                hoverEnabled: true
                cursorShape: Qt.PointingHandCursor

                onEntered: {
                    itemRoot.isHovered = true
                }

                onExited: {
                    itemRoot.isHovered = false
                }

                onPressed: function(mouse) {
                    // Iniciar efecto de luz
                    itemRoot.lightEffectCenter = Qt.point(mouse.x, mouse.y)
                    itemRoot.lightEffectActive = true
                    lightAnimation.start()
                    
                    // Buscar el índice del elemento en la lista correspondiente
                    var index = -1
                    var parentList = itemRoot.ListView.view
                    
                    if (parentList === songsListView) {
                        // Buscar en la lista de canciones
                        for (var i = 0; i < songsListView.songsModel.length; i++) {
                            if (songsListView.songsModel[i].title === itemRoot.title && 
                                songsListView.songsModel[i].artist === itemRoot.artist) {
                                index = i
                                break
                            }
                        }
                        
                        if (index >= 0) {
                            // Programar la selección del elemento
                            selectionTimer.itemIndex = index
                            selectionTimer.isSongList = true
                            selectionTimer.start()
                        }
                    } else if (parentList === librariesListView) {
                        // Buscar en la lista de bibliotecas
                        for (var j = 0; j < librariesListView.librariesModel.length; j++) {
                            if (librariesListView.librariesModel[j].title === itemRoot.title && 
                                librariesListView.librariesModel[j].artist === itemRoot.artist) {
                                index = j
                                break
                            }
                        }
                        
                        if (index >= 0) {
                            // Programar la selección del elemento
                            selectionTimer.itemIndex = index
                            selectionTimer.isSongList = false
                            selectionTimer.start()
                        }
                    }
                }
            }

            // Animación del efecto de luz
            NumberAnimation {
                id: lightAnimation
                target: itemRoot
                property: "lightEffectProgress"
                from: 0.0
                to: 1.0
                duration: 1200
                easing.type: Easing.InOutCubic

                onFinished: {
                    // Iniciar desvanecimiento
                    fadeOutAnimation.start()
                }
            }

            // Animación de desvanecimiento
            NumberAnimation {
                id: fadeOutAnimation
                target: itemRoot
                property: "lightEffectProgress"
                to: 0.0
                duration: 300
                easing.type: Easing.OutQuad

                onFinished: {
                    itemRoot.lightEffectActive = false
                }
            }

            // Animación de selección mejorada
            SequentialAnimation {
                id: selectionAnimation
                
                // Primer paso: escalar hacia arriba con un efecto de rebote
                NumberAnimation {
                    target: itemBackground
                    property: "scale"
                    from: 1.0
                    to: 1.05
                    duration: 200
                    easing.type: Easing.OutCubic
                }
                
                // Segundo paso: escalar hacia abajo con un efecto de rebote
                NumberAnimation {
                    target: itemBackground
                    property: "scale"
                    from: 1.05
                    to: 1.0
                    duration: 250
                    easing.type: Easing.OutBounce
                }
                
                // Finalizar la animación
                ScriptAction {
                    script: {
                        itemRoot.animatingSelection = false
                    }
                }
            }
            
            // Animación de deselección
            SequentialAnimation {
                id: deselectionAnimation
            
                // Primer paso: escalar hacia abajo ligeramente
                NumberAnimation {
                    target: itemBackground
                    property: "scale"
                    from: 1.0
                    to: 0.98
                    duration: 150
                    easing.type: Easing.OutCubic
                }
            
                // Segundo paso: volver a la escala normal
                NumberAnimation {
                    target: itemBackground
                    property: "scale"
                    from: 0.98
                    to: 1.0
                    duration: 200
                    easing.type: Easing.OutCubic
                }
            
                // Finalizar la animación
                ScriptAction {
                    script: {
                        itemRoot.animatingSelection = false
                    }
                }
            }

            // Timer para la selección del elemento
            Timer {
                id: selectionTimer
                interval: 450
                repeat: false
                
                property int itemIndex: -1
                property bool isSongList: true
                
                onTriggered: {
                    if (isSongList) {
                        root.selectSongItem(itemIndex)
                    } else {
                        root.selectLibraryItem(itemIndex)
                    }
                }
            }
        }
    }

    // Animación para el desplazamiento horizontal
    SequentialAnimation {
        id: horizontalSlideAnimation
        
        // Cambiar el estado primero
        ScriptAction {
            script: {
                root.showLibraries = !root.showLibraries
            }
        }
        
        // La animación del desplazamiento se maneja automáticamente por el Behavior en slidingContainer
    }

    // Función para seleccionar un elemento de la lista de canciones
    function selectSongItem(index) {
        // Crear una copia del modelo para modificarlo
        var updatedModel = [...songsListView.songsModel]
        
        // Encontrar el índice del elemento activo actual
        var previousActiveIndex = -1
        for (var i = 0; i < updatedModel.length; i++) {
            if (updatedModel[i].active) {
                previousActiveIndex = i
                break
            }
        }
        
        // Si el elemento seleccionado ya está activo, no hacemos nada
        if (previousActiveIndex === index) {
            return
        }
        
        // Desactivar todos los elementos
        for (var i = 0; i < updatedModel.length; i++) {
            updatedModel[i].active = false
        }
        
        // Activar el elemento seleccionado
        if (index >= 0 && index < updatedModel.length) {
            updatedModel[index].active = true
            root.currentIndex = index
            root.songSelected(index)
            
            // Actualizar el modelo sin parpadeo
            songsListView.model = null
            songsListView.songsModel = updatedModel
            songsListView.model = updatedModel
            
            // Asegurar que el elemento seleccionado sea visible
            songsListView.positionViewAtIndex(index, ListView.Contain)
            
            // Mover el selector móvil al nuevo item (solo si estamos en la vista de canciones)
            if (!root.showLibraries) {
                movingSelector.moveToIndex(index, songsListView)
            }
        }
    }

    // Función para seleccionar un elemento de la lista de bibliotecas
    function selectLibraryItem(index) {
        // Crear una copia del modelo para modificarlo
        var updatedModel = [...librariesListView.librariesModel]
        
        // Encontrar el índice del elemento activo actual
        var previousActiveIndex = -1
        for (var i = 0; i < updatedModel.length; i++) {
            if (updatedModel[i].active) {
                previousActiveIndex = i
                break
            }
        }
        
        // Si el elemento seleccionado ya está activo, no hacemos nada
        if (previousActiveIndex === index) {
            return
        }
        
        // Desactivar todos los elementos
        for (var i = 0; i < updatedModel.length; i++) {
            updatedModel[i].active = false
        }
        
        // Activar el elemento seleccionado
        if (index >= 0 && index < updatedModel.length) {
            updatedModel[index].active = true
            root.currentIndex = index
            
            // Actualizar el modelo sin parpadeo
            librariesListView.model = null
            librariesListView.librariesModel = updatedModel
            librariesListView.model = updatedModel
            
            // Asegurar que el elemento seleccionado sea visible
            librariesListView.positionViewAtIndex(index, ListView.Contain)
            
            // Mover el selector móvil al nuevo item (solo si estamos en la vista de bibliotecas)
            if (root.showLibraries) {
                movingSelector.moveToIndex(index, librariesListView)
            }
        }
    }

    // Funciones
    function toggleViewMode() {
        horizontalSlideAnimation.start()
        
        // Ocultar temporalmente el selector durante la transición
        movingSelector.visible = false
        
        // Después de la transición, reposicionar el selector si hay un elemento activo
        Qt.callLater(function() {
            var activeIndex = -1
            var targetListView = null
            
            if (root.showLibraries) {
                // Buscar elemento activo en bibliotecas
                for (var i = 0; i < librariesListView.librariesModel.length; i++) {
                    if (librariesListView.librariesModel[i].active) {
                        activeIndex = i
                        targetListView = librariesListView
                        break
                    }
                }
            } else {
                // Buscar elemento activo en canciones
                for (var j = 0; j < songsListView.songsModel.length; j++) {
                    if (songsListView.songsModel[j].active) {
                        activeIndex = j
                        targetListView = songsListView
                        break
                    }
                }
            }
            
            // Reposicionar el selector si hay un elemento activo
            if (activeIndex >= 0 && targetListView) {
                movingSelector.moveToIndex(activeIndex, targetListView)
            }
        })
    }
}
