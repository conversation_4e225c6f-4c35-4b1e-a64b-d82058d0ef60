import QtQuick
import QtQuick.Controls
import QtQuick.Layouts
import QtQuick.Effects

Item {
    id: root
    width: 600
    height: 400

    // Propiedades
    property string albumCoverSource: ""
    property string songTitle: "Sin título"
    property string artistName: "Desconocido"
    property string albumName: "Desconocido"
    property bool isPlaying: false
    property bool isExpanded: true
    property int currentPosition: 0
    property int totalDuration: 0

    // Propiedades para animaciones
    property real controlsOpacity: isExpanded ? 1.0 : 0.0
    property real infoOpacity: isExpanded ? 0.0 : 1.0
    property real albumScale: isExpanded ? 1.0 : 0.55
    property real controlsScale: isExpanded ? 1.0 : 0.05
    property real seekbarScale: isExpanded ? 1.0 : 0.05
    property real infoScale: isExpanded ? 0.0 : 1.0

    // Se<PERSON>les
    signal playPauseClicked()
    signal nextClicked()
    signal previousClicked()
    signal seekChanged(int position)
    signal expandToggled(bool expanded)

    // Rectángulo de fondo con bordes redondeados
    Rectangle {
        id: background
        anchors.fill: parent
        color: "#1E1E1E"
        radius: 20

        // Efecto de sombra
        layer.enabled: true
        layer.effect: MultiEffect {
            shadowEnabled: true
            shadowHorizontalOffset: 0
            shadowVerticalOffset: 4
            shadowBlur: 12.0
            shadowColor: "#40000000"
        }
    }

    // Contenido principal
    Item {
        anchors.fill: parent
        anchors.margins: 20

        // Carátula del álbum
        AlbumCover {
            id: albumCover
            width: isExpanded ? Math.min(parent.width * 0.4, parent.height * 0.6) : 82
            height: width
            anchors.horizontalCenter: parent.horizontalCenter
            anchors.top: parent.top
            anchors.topMargin: 10

            imageSource: root.albumCoverSource
            isPlaying: root.isPlaying

            // Animaciones de transición
            Behavior on width {
                NumberAnimation {
                    duration: 320
                    easing.type: Easing.OutCubic
                }
            }

            Behavior on height {
                NumberAnimation {
                    duration: 320
                    easing.type: Easing.OutCubic
                }
            }

            Behavior on anchors.topMargin {
                NumberAnimation {
                    duration: 320
                    easing.type: Easing.OutCubic
                }
            }

            // Interacciones
            onClicked: {
                root.expandToggled(!root.isExpanded)
                animatePulse()
            }
        }

        // Información de la canción (visible cuando está minimizado)
        ColumnLayout {
            id: songInfo
            anchors.left: albumCover.right
            anchors.leftMargin: 15
            anchors.verticalCenter: albumCover.verticalCenter
            width: parent.width - albumCover.width - 30
            spacing: 5
            opacity: root.infoOpacity
            visible: opacity > 0

            // Título de la canción
            Label {
                id: titleLabel
                text: root.songTitle
                font.pixelSize: 18
                font.bold: true
                color: "#FFFFFF"
                elide: Text.ElideRight
                Layout.fillWidth: true
            }

            // Artista
            Label {
                id: artistLabel
                text: root.artistName
                font.pixelSize: 14
                color: "#CCCCCC"
                elide: Text.ElideRight
                Layout.fillWidth: true
            }

            // Álbum
            Label {
                id: albumLabel
                text: root.albumName
                font.pixelSize: 12
                color: "#AAAAAA"
                elide: Text.ElideRight
                Layout.fillWidth: true
            }

            // Animación de transición
            Behavior on opacity {
                NumberAnimation {
                    duration: 220
                    easing.type: Easing.InOutCubic
                }
            }

            transform: Scale {
                xScale: root.infoScale
                yScale: root.infoScale
                origin.x: 0
                origin.y: songInfo.height / 2
            }

            Behavior on scale {
                NumberAnimation {
                    duration: 320
                    easing.type: Easing.OutCubic
                }
            }
        }

        // Barra de progreso
        CustomSlider {
            id: progressSlider
            anchors.top: albumCover.bottom
            anchors.topMargin: 20
            anchors.left: parent.left
            anchors.right: parent.right
            height: 50

            from: 0
            to: root.totalDuration > 0 ? root.totalDuration : 100
            value: root.currentPosition
            opacity: root.controlsOpacity

            onMoved: function(value) {
                root.seekChanged(value)
            }

            // Animaciones de transición
            Behavior on opacity {
                NumberAnimation {
                    duration: 220
                    easing.type: Easing.InOutCubic
                }
            }

            transform: Scale {
                xScale: root.seekbarScale
                yScale: root.seekbarScale
                origin.x: progressSlider.width / 2
                origin.y: progressSlider.height / 2
            }

            Behavior on scale {
                NumberAnimation {
                    duration: 320
                    easing.type: Easing.OutCubic
                }
            }
        }

        // Controles de reproducción
        Row {
            id: playbackControls
            anchors.top: progressSlider.bottom
            anchors.topMargin: 15
            anchors.horizontalCenter: parent.horizontalCenter
            spacing: 15
            opacity: root.controlsOpacity

            // Botón de LastFM
            PlaybackButton {
                id: lastfmButton
                iconSource: "file:qml/assets/lastfm.svg"
                width: 40
                height: 40
                property bool isActive: false

                onClicked: {
                    isActive = !isActive
                    toggle()
                    animatePulse()
                    console.log("LastFM:", isActive ? "Connected" : "Disconnected")
                }
            }

            // Botón de favorito
            PlaybackButton {
                id: favoriteButton
                iconSource: "file:qml/assets/heart_outline.svg"
                width: 40
                height: 40
                property bool isFavorite: false

                onClicked: {
                    isFavorite = !isFavorite
                    iconSource = isFavorite ? "file:qml/assets/heart_filled.svg" : "file:qml/assets/heart_outline.svg"
                    toggle()
                    animatePulse()
                    console.log("Favorite:", isFavorite ? "Added to favorites" : "Removed from favorites")
                }
            }

            // Botón de aleatorio
            PlaybackButton {
                id: shuffleButton
                iconSource: "file:qml/assets/shuffle.svg"
                width: 40
                height: 40
                property bool isShuffled: false

                onClicked: {
                    isShuffled = !isShuffled
                    toggle()
                    animatePulse()
                    console.log("Shuffle mode:", isShuffled ? "ON" : "OFF")
                }
            }

            // Botón anterior
            PlaybackButton {
                id: prevButton
                iconSource: "file:qml/assets/prev.svg"
                width: 40
                height: 40
                onClicked: {
                    root.previousClicked()
                    animatePulse()
                }
            }

            // Botón reproducir/pausar
            PlaybackButton {
                id: playPauseButton
                iconSource: "file:qml/assets/play_arrow.svg"
                width: 40
                height: 40
                property bool isPlaying: false

                // Actualizar el icono cuando cambia el estado de reproducción desde fuera
                Connections {
                    target: root
                    function onIsPlayingChanged() {
                        playPauseButton.isPlaying = root.isPlaying
                        playPauseButton.iconSource = root.isPlaying ?
                            "file:qml/assets/pause.svg" :
                            "file:qml/assets/play_arrow.svg"
                    }
                }

                onClicked: {
                    // Cambiar el estado localmente
                    isPlaying = !isPlaying

                    // Actualizar el icono
                    iconSource = isPlaying ?
                        "file:qml/assets/pause.svg" :
                        "file:qml/assets/play_arrow.svg"

                    // Notificar al backend
                    root.playPauseClicked()

                    // Animar el botón
                    animatePulse()
                }
            }

            // Botón siguiente
            PlaybackButton {
                id: nextButton
                iconSource: "file:qml/assets/next.svg"
                width: 40
                height: 40
                onClicked: {
                    root.nextClicked()
                    animatePulse()
                }
            }

            // Botón de repetir
            PlaybackButton {
                id: repeatButton
                iconSource: "file:qml/assets/repeat.svg"
                width: 40
                height: 40
                property bool isLooped: false

                onClicked: {
                    isLooped = !isLooped
                    toggle()
                    animatePulse()
                    console.log("Repeat mode:", isLooped ? "ON" : "OFF")
                }
            }

            // Botón de cambiar vista
            PlaybackButton {
                id: viewButton
                iconSource: "file:qml/assets/view_eye.svg"
                width: 40
                height: 40
                property bool alternateView: false

                onClicked: {
                    alternateView = !alternateView
                    iconSource = alternateView ? "file:qml/assets/view_eye_blink.svg" : "file:qml/assets/view_eye.svg"
                    animatePulse()
                    console.log("View mode changed:", alternateView ? "Alternate" : "Normal")
                }
            }

            // Animaciones de transición
            Behavior on opacity {
                NumberAnimation {
                    duration: 220
                    easing.type: Easing.InOutCubic
                }
            }

            transform: Scale {
                xScale: root.controlsScale
                yScale: root.controlsScale
                origin.x: playbackControls.width / 2
                origin.y: playbackControls.height / 2
            }

            Behavior on scale {
                NumberAnimation {
                    duration: 320
                    easing.type: Easing.OutCubic
                }
            }
        }
    }
}
