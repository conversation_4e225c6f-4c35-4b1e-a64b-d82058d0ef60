import QtQuick
import QtQuick.Window
import QtQuick.Controls
import QtQuick.Layouts
import QtQuick.Effects
import components
import styles
import "components" as Components
import "animations" as Animations

Window {
    id: mainWindow
    width: 950
    height: 800
    visible: true
    title: "Reproductor de Música"
    color: "#121212"  // Fondo oscuro para la aplicación

    // Propiedades para el estado de la aplicación
    property bool isPlaying: false
    property bool isExpanded: true
    property string currentSong: "Sin título"
    property string currentArtist: "Desconocido"
    property string currentAlbum: "Desconocido"
    property string albumCoverSource: ""
    property int currentPosition: 0
    property int totalDuration: 0
    property bool playlistVisible: false
    property var lyricsModel: []
    property var playlistData: []

    // Señales para comunicarse con el backend
    signal playPauseRequested()
    signal nextRequested()
    signal previousRequested()
    signal seekRequested(int position)
    signal togglePlaylistRequested()
    signal songSelected(int index)

    // Layout principal horizontal
    RowLayout {
        anchors.fill: parent
        spacing: 0

        // Área principal (letras, carátula del álbum y controles)
        Item {
            Layout.fillHeight: true
            Layout.fillWidth: true
            Layout.margins: 20
            
            // Ancho dinámico: se reduce cuando el panel está visible
            Layout.preferredWidth: playlistVisible ? 
                mainWindow.width - 320 : // Ancho reducido cuando el panel está visible (300 + 20 de margen)
                mainWindow.width - 40    // Ancho completo menos márgenes cuando el panel está oculto

            // Animación suave para el cambio de ancho
            Behavior on Layout.preferredWidth {
                NumberAnimation {
                    duration: 300
                    easing.type: Easing.OutCubic
                }
            }

            // Contenedor principal unificado
            Rectangle {
                id: mainContainer
                anchors.fill: parent
                color: "#1E1E1E"
                radius: 20

                // Efecto de sombra
                layer.enabled: true
                layer.effect: MultiEffect {
                    shadowEnabled: true
                    shadowHorizontalOffset: 0
                    shadowVerticalOffset: 4
                    shadowBlur: 12.0
                    shadowColor: "#40000000"
                }

                // Contenido principal
                Item {
                    anchors.fill: parent
                    anchors.margins: 20

                    // Componente de animaciones de karaoke (arriba)
                    Animations.KaraokeAnimations {
                        id: karaokeView
                        anchors.top: parent.top
                        anchors.left: parent.left
                        anchors.right: parent.right
                        // Altura dinámica: se expande cuando la carátula se contrae
                        height: albumInfoContainer.isExpanded ?
                                parent.height * 0.55 : // Altura normal cuando la carátula está expandida
                                parent.height * 0.8    // Altura aumentada cuando la carátula está contraída

                        // Enlazar el estado de la carátula con la propiedad del componente
                        albumCoverExpanded: albumInfoContainer.isExpanded

                        // Animación suave para la transición de altura
                        Behavior on height {
                            NumberAnimation {
                                duration: 350
                                easing.type: Easing.OutCubic
                            }
                        }
                        lyricsModel: mainWindow.lyricsModel
                        currentIndex: 0
                        progress: mainWindow.currentPosition / (mainWindow.totalDuration > 0 ? mainWindow.totalDuration : 100)

                        onLineClicked: function(index) {
                            console.log("Línea seleccionada:", index)
                            currentIndex = index
                        }

                        // Depuración
                        Component.onCompleted: {
                            console.log("KaraokeAnimations completado. Modelo:", lyricsModel ? "disponible" : "no disponible")
                            if (lyricsModel) {
                                console.log("Número de líneas:", lyricsModel.length)
                            }

                            // Si no hay letras, mostrar letras de ejemplo
                            if (!lyricsModel || lyricsModel.length === 0) {
                                var exampleLyrics = [
                                    "Bienvenido al reproductor de música",
                                    "Esta es una canción de ejemplo",
                                    "Con letras para mostrar la funcionalidad de karaoke",
                                    "Puedes hacer clic en una línea para seleccionarla",
                                    "Las letras se sincronizarán con la música"
                                ]
                                mainWindow.lyricsModel = exampleLyrics
                            }
                        }
                    }

                    // Contenedor para la carátula del álbum y la información de la canción
                    Item {
                        id: albumInfoContainer
                        // Posicionamiento dinámico: se desplaza hacia abajo cuando está contraído
                        anchors.top: karaokeView.bottom
                        anchors.topMargin: isExpanded ? 20 : -40  // Margen negativo para desplazarse hacia arriba cuando está contraído

                        // Animación suave para la transición del margen superior
                        Behavior on anchors.topMargin {
                            NumberAnimation {
                                duration: 350
                                easing.type: Easing.OutCubic
                            }
                        }
                        anchors.left: parent.left
                        anchors.right: parent.right
                        height: albumCover.height

                        // Propiedad para controlar la expansión
                        property bool isExpanded: false

                        // Inicializar el estado expandido cuando se carga el componente
                        Component.onCompleted: {
                            // Establecer un retraso para permitir que la interfaz se cargue completamente
                            // y evitar cambios de estado durante la inicialización
                            albumInfoContainer.isExpanded = false  // Establecer estado inicial inmediatamente
                            expandTimer.start()
                        }

                        // Temporizador para estabilizar el estado inicial
                        Timer {
                            id: expandTimer
                            interval: 800  // Retraso más largo para asegurar estabilidad
                            repeat: false
                            onTriggered: {
                                // Asegurar que estamos en estado contraído después de la inicialización
                                albumInfoContainer.isExpanded = false

                                // Habilitar las animaciones después de la inicialización
                                albumInfoContainer.animationsEnabled = true
                            }
                        }

                        // Propiedad para controlar si las animaciones están habilitadas
                        property bool animationsEnabled: false

                        // Carátula del álbum
                        Components.AlbumCover {
                            id: albumCover
                            anchors.left: parent.left
                            anchors.leftMargin: 20
                            anchors.verticalCenter: parent.verticalCenter
                            width: 140
                            height: width
                            scale: albumInfoContainer.isExpanded ? 1.0 : 82/140
                            transformOrigin: Item.Left

                            imageSource: mainWindow.albumCoverSource
                            isPlaying: mainWindow.isPlaying

                            // Área para detectar hover
                            MouseArea {
                                id: albumHoverArea
                                anchors.fill: parent
                                hoverEnabled: true

                                // Temporizadores para estabilizar el comportamiento de hover
                                Timer {
                                    id: hoverEnterTimer
                                    interval: 50  // Pequeño retraso para evitar activaciones accidentales
                                    repeat: false
                                    onTriggered: {
                                        albumInfoContainer.isExpanded = true
                                        // Detener cualquier temporizador de contracción automática
                                        autoCollapseTimer.stop()
                                    }
                                }

                                Timer {
                                    id: hoverExitTimer
                                    interval: 100  // Retraso ligeramente mayor para salida
                                    repeat: false
                                    onTriggered: {
                                        // Iniciar temporizador de contracción automática
                                        autoCollapseTimer.start()
                                    }
                                }

                                // Temporizador para contracción automática después de 3 segundos
                                Timer {
                                    id: autoCollapseTimer
                                    interval: 3000  // 3 segundos
                                    repeat: false
                                    onTriggered: {
                                        albumInfoContainer.isExpanded = false
                                    }
                                }

                                onEntered: {
                                    // Cancelar cualquier temporizador de salida pendiente
                                    hoverExitTimer.stop()
                                    // Cancelar cualquier temporizador de contracción automática
                                    autoCollapseTimer.stop()
                                    // Iniciar temporizador de entrada
                                    hoverEnterTimer.start()
                                }

                                onExited: {
                                    // Cancelar cualquier temporizador de entrada pendiente
                                    hoverEnterTimer.stop()
                                    // Iniciar temporizador de salida
                                    hoverExitTimer.start()
                                }

                                onClicked: {
                                    // Activar la animación de pulso específica para la carátula
                                    albumPulseAnimation.start()
                                }
                            }

                            // Animaciones de transición para la escala
                            Behavior on scale {
                                enabled: albumInfoContainer.animationsEnabled
                                NumberAnimation {
                                    duration: 350
                                    easing.type: Easing.OutCubic
                                }
                            }

                            // Animación de pulso específica para la carátula
                            SequentialAnimation {
                                id: albumPulseAnimation

                                PropertyAnimation {
                                    target: albumCover
                                    property: "scale"
                                    from: albumInfoContainer.isExpanded ? 1.0 : 82/140
                                    to: albumInfoContainer.isExpanded ? 1.05 : (82/140) * 1.05
                                    duration: 100
                                    easing.type: Easing.OutQuad
                                }

                                PropertyAnimation {
                                    target: albumCover
                                    property: "scale"
                                    from: albumInfoContainer.isExpanded ? 1.05 : (82/140) * 1.05
                                    to: albumInfoContainer.isExpanded ? 1.0 : 82/140
                                    duration: 100
                                    easing.type: Easing.InOutQuad
                                }
                            }
                        }

                        // Información de la canción (visible siempre)
                        ColumnLayout {
                            id: songInfo
                            // Posicionamiento dinámico: se desplaza a la izquierda cuando la carátula se contrae
                            x: albumCover.x + (albumCover.width * albumCover.scale) + 15 // Ajustar según el tamaño escalado real
                            anchors.verticalCenter: albumCover.verticalCenter
                            width: parent.width - (albumCover.x + (albumCover.width * albumCover.scale) + 15) - 20 // Ajustar según posición real
                            spacing: albumInfoContainer.isExpanded ? 5 : 2

                            // Animaciones de transición
                            Behavior on spacing {
                                enabled: albumInfoContainer.animationsEnabled
                                NumberAnimation {
                                    duration: 250
                                    easing.type: Easing.OutCubic
                                }
                            }

                            Behavior on width {
                                enabled: albumInfoContainer.animationsEnabled
                                NumberAnimation {
                                    duration: 250
                                    easing.type: Easing.OutCubic
                                }
                            }

                            Behavior on x {
                                enabled: albumInfoContainer.animationsEnabled
                                NumberAnimation {
                                    duration: 250
                                    easing.type: Easing.OutCubic
                                }
                            }

                            // Título de la canción
                            Label {
                                id: titleLabel
                                text: mainWindow.currentSong
                                font.pixelSize: albumInfoContainer.isExpanded ? 18 : 15
                                font.bold: true
                                color: "#FFFFFF"
                                elide: Text.ElideRight
                                Layout.fillWidth: true
                                horizontalAlignment: albumInfoContainer.isExpanded ? Text.AlignLeft : Text.AlignLeft

                                // Animaciones de transición
                                Behavior on font.pixelSize {
                                    enabled: albumInfoContainer.animationsEnabled
                                    NumberAnimation {
                                        duration: 250
                                        easing.type: Easing.OutCubic
                                    }
                                }

                                Behavior on horizontalAlignment {
                                    enabled: albumInfoContainer.animationsEnabled
                                    NumberAnimation {
                                        duration: 250
                                        easing.type: Easing.OutCubic
                                    }
                                }
                            }

                            // Artista
                            Label {
                                id: artistLabel
                                text: mainWindow.currentArtist
                                font.pixelSize: albumInfoContainer.isExpanded ? 14 : 12
                                color: "#CCCCCC"
                                elide: Text.ElideRight
                                Layout.fillWidth: true
                                horizontalAlignment: albumInfoContainer.isExpanded ? Text.AlignLeft : Text.AlignLeft

                                // Animaciones de transición
                                Behavior on font.pixelSize {
                                    enabled: albumInfoContainer.animationsEnabled
                                    NumberAnimation {
                                        duration: 250
                                        easing.type: Easing.OutCubic
                                    }
                                }

                                Behavior on horizontalAlignment {
                                    enabled: albumInfoContainer.animationsEnabled
                                    NumberAnimation {
                                        duration: 250
                                        easing.type: Easing.OutCubic
                                    }
                                }
                            }

                            // Álbum (visible solo cuando la carátula está expandida)
                            Label {
                                id: albumLabel
                                text: mainWindow.currentAlbum
                                font.pixelSize: 12
                                color: "#AAAAAA"
                                elide: Text.ElideRight
                                Layout.fillWidth: true
                                visible: text.length > 0 && albumInfoContainer.isExpanded
                                horizontalAlignment: Text.AlignLeft
                                opacity: albumInfoContainer.isExpanded ? 1.0 : 0.0

                                // Animaciones de transición
                                Behavior on font.pixelSize {
                                    enabled: albumInfoContainer.animationsEnabled
                                    NumberAnimation {
                                        duration: 250
                                        easing.type: Easing.OutCubic
                                    }
                                }

                                Behavior on opacity {
                                    enabled: albumInfoContainer.animationsEnabled
                                    NumberAnimation {
                                        duration: 250
                                        easing.type: Easing.OutCubic
                                    }
                                }
                            }

                            // Etiqueta de información adicional (visible solo cuando la carátula está contraída)
                            Item {
                                Layout.fillWidth: true
                                Layout.preferredHeight: 10
                                visible: !albumInfoContainer.isExpanded
                                opacity: albumInfoContainer.isExpanded ? 0.0 : 1.0

                                // Animación de transición para la opacidad
                                Behavior on opacity {
                                    enabled: albumInfoContainer.animationsEnabled
                                    NumberAnimation {
                                        duration: 250
                                        easing.type: Easing.OutCubic
                                    }
                                }

                                Text {
                                    id: lyricsInfoText
                                    anchors.right: parent.right
                                    anchors.bottom: parent.bottom
                                    text: "Lyrics provided by LRC file"
                                    font.pixelSize: 10
                                    color: "#787878"
                                    opacity: 0.28

                                    // Animaciones de transición
                                    Behavior on opacity {
                                        enabled: albumInfoContainer.animationsEnabled
                                        NumberAnimation {
                                            duration: 250
                                            easing.type: Easing.OutCubic
                                        }
                                    }
                                }
                            }
                        }
                    }

                    // Barra de progreso
                    Components.CustomSlider {
                        id: progressSlider
                        anchors.top: albumInfoContainer.bottom
                        anchors.topMargin: albumInfoContainer.isExpanded ? 15 : -10  // Margen negativo cuando está contraído
                        anchors.left: parent.left
                        anchors.right: parent.right
                        height: 50
                        opacity: albumInfoContainer.isExpanded ? 1.0 : 0.0
                        y: albumInfoContainer.isExpanded ? 0 : -30
                        visible: opacity > 0.01

                        from: 0
                        to: mainWindow.totalDuration > 0 ? mainWindow.totalDuration : 100
                        value: mainWindow.currentPosition

                        onMoved: function(value) {
                            mainWindow.seekRequested(value)
                        }

                        // MouseArea para manejar hover y controlar el temporizador de contracción
                        MouseArea {
                            anchors.fill: parent
                            hoverEnabled: true
                            propagateComposedEvents: true

                            onEntered: {
                                // Cancelar cualquier temporizador de contracción automática
                                if (albumInfoContainer.isExpanded) {
                                    autoCollapseTimer.stop()
                                }
                            }

                            onExited: {
                                // Iniciar temporizador de contracción automática
                                if (albumInfoContainer.isExpanded) {
                                    autoCollapseTimer.start()
                                }
                            }

                            // Permitir que los eventos pasen al slider
                            onPressed: function(mouse) { mouse.accepted = false }
                            onReleased: function(mouse) { mouse.accepted = false }
                            onClicked: function(mouse) { mouse.accepted = false }
                            onPositionChanged: function(mouse) { mouse.accepted = false }
                        }

                        // Animaciones de transición
                        Behavior on opacity {
                            enabled: albumInfoContainer.animationsEnabled
                            NumberAnimation {
                                duration: 350
                                easing.type: Easing.OutCubic
                            }
                        }

                        Behavior on y {
                            enabled: albumInfoContainer.animationsEnabled
                            NumberAnimation {
                                duration: 350
                                easing.type: Easing.OutCubic
                            }
                        }
                    }

                    // Contenedor para los controles de reproducción
                    Item {
                        id: playbackControlsContainer
                        anchors.top: progressSlider.bottom
                        anchors.topMargin: albumInfoContainer.isExpanded ? 15 : -10  // Margen negativo cuando está contraído
                        anchors.horizontalCenter: parent.horizontalCenter
                        width: playbackControls.width
                        height: playbackControls.height
                        opacity: albumInfoContainer.isExpanded ? 1.0 : 0.0
                        y: albumInfoContainer.isExpanded ? 0 : -30
                        visible: opacity > 0.01

                        // Área para detectar hover en los controles de reproducción
                        MouseArea {
                            anchors.fill: parent
                            hoverEnabled: true
                            propagateComposedEvents: true

                            onEntered: {
                                // Cancelar cualquier temporizador de contracción automática
                                if (albumInfoContainer.isExpanded) {
                                    autoCollapseTimer.stop()
                                }
                            }

                            onExited: {
                                // Iniciar temporizador de contracción automática
                                if (albumInfoContainer.isExpanded) {
                                    autoCollapseTimer.start()
                                }
                            }

                            // Permitir que los eventos pasen a través para que los botones funcionen
                            onPressed: function(mouse) { mouse.accepted = false }
                            onReleased: function(mouse) { mouse.accepted = false }
                            onClicked: function(mouse) { mouse.accepted = false }
                        }

                        // Animaciones de transición para el contenedor
                        Behavior on opacity {
                            enabled: albumInfoContainer.animationsEnabled
                            NumberAnimation {
                                duration: 350
                                easing.type: Easing.OutCubic
                            }
                        }

                        Behavior on y {
                            enabled: albumInfoContainer.animationsEnabled
                            NumberAnimation {
                                duration: 350
                                easing.type: Easing.OutCubic
                            }
                        }

                        // Controles de reproducción en un Row
                        Row {
                            id: playbackControls
                            spacing: 15

                            // Botón de LastFM
                            Components.PlaybackButton {
                                id: lastfmButton
                                iconSource: "file:qml/assets/lastfm.svg"
                                width: 35
                                height: 35
                                property bool isActive: false

                                onClicked: {
                                    isActive = !isActive
                                    toggle()
                                    animatePulse()
                                    console.log("LastFM:", isActive ? "Connected" : "Disconnected")
                                }
                            }

                            // Botón de favorito
                            Components.PlaybackButton {
                                id: favoriteButton
                                iconSource: "file:qml/assets/heart_outline.svg"
                                width: 35
                                height: 35
                                property bool isFavorite: false

                                onClicked: {
                                    isFavorite = !isFavorite
                                    iconSource = isFavorite ? "file:qml/assets/heart_filled.svg" : "file:qml/assets/heart_outline.svg"
                                    toggle()
                                    animatePulse()
                                    console.log("Favorite:", isFavorite ? "Added to favorites" : "Removed from favorites")
                                }
                            }

                            // Botón de aleatorio
                            Components.PlaybackButton {
                                id: shuffleButton
                                iconSource: "file:qml/assets/shuffle.svg"
                                width: 35
                                height: 35
                                property bool isShuffled: false

                                onClicked: {
                                    isShuffled = !isShuffled
                                    toggle()
                                    animatePulse()
                                    console.log("Shuffle mode:", isShuffled ? "ON" : "OFF")
                                }
                            }

                            // Botón anterior
                            Components.PlaybackButton {
                                id: prevButton
                                iconSource: "file:qml/assets/prev.svg"
                                width: 35
                                height: 35
                                onClicked: {
                                    mainWindow.previousRequested()
                                    animatePulse()
                                }
                            }

                            // Botón reproducir/pausar
                            Components.PlaybackButton {
                                id: playPauseButton
                                iconSource: mainWindow.isPlaying ? "file:qml/assets/pause.svg" : "file:qml/assets/play_arrow.svg"
                                width: 35
                                height: 35
                                property bool isPlaying: mainWindow.isPlaying

                                // Actualizar el icono cuando cambia el estado de reproducción desde fuera
                                Connections {
                                    target: mainWindow
                                    function onIsPlayingChanged() {
                                        playPauseButton.iconSource = mainWindow.isPlaying ?
                                            "file:qml/assets/pause.svg" :
                                            "file:qml/assets/play_arrow.svg"
                                    }
                                }

                                onClicked: {
                                    // Toggle the playing state locally
                                    isPlaying = !isPlaying

                                    // Update the icon immediately
                                    iconSource = isPlaying ?
                                        "file:qml/assets/pause.svg" :
                                        "file:qml/assets/play_arrow.svg"

                                    // Notify the backend
                                    mainWindow.playPauseRequested()
                                    animatePulse()
                                }
                            }

                            // Botón siguiente
                            Components.PlaybackButton {
                                id: nextButton
                                iconSource: "file:qml/assets/next.svg"
                                width: 35
                                height: 35
                                onClicked: {
                                    mainWindow.nextRequested()
                                    animatePulse()
                                }
                            }

                            // Botón de repetir
                            Components.PlaybackButton {
                                id: repeatButton
                                iconSource: "file:qml/assets/repeat.svg"
                                width: 35
                                height: 35
                                property bool isLooped: false

                                onClicked: {
                                    isLooped = !isLooped
                                    toggle()
                                    animatePulse()
                                    console.log("Repeat mode:", isLooped ? "ON" : "OFF")
                                }
                            }

                            // Botón de cambiar vista
                            Components.PlaybackButton {
                                id: viewButton
                                iconSource: "file:qml/assets/view_eye.svg"
                                width: 35
                                height: 35
                                property bool alternateView: false

                                onClicked: {
                                    alternateView = !alternateView
                                    iconSource = alternateView ? "file:qml/assets/view_eye_blink.svg" : "file:qml/assets/view_eye.svg"
                                    animatePulse()
                                    console.log("View mode changed:", alternateView ? "Alternate" : "Normal")
                                }
                            }
                        }
                    }
                }
            }
        }

        // Panel lateral de lista de reproducción
        PlaylistPanel {
            id: playlistPanel
            Layout.fillHeight: true
            Layout.preferredWidth: playlistVisible ? 300 : 0
            visible: Layout.preferredWidth > 0
            playlistModel: mainWindow.playlistData

            // Depuración
            Component.onCompleted: {
                console.log("PlaylistPanel completado. Modelo:", playlistModel ? "disponible" : "no disponible")
                if (playlistModel) {
                    console.log("Número de elementos en playlistPanel:", playlistModel.length)
                }
            }

            onSongSelected: function(index) {
                mainWindow.songSelected(index)
            }

            onPanelToggled: function(expanded) {
                mainWindow.playlistVisible = expanded
            }

            // Animación de apertura/cierre
            Behavior on Layout.preferredWidth {
                NumberAnimation {
                    duration: 300
                    easing.type: Easing.OutCubic
                }
            }
        }
    }

    // Botón para mostrar/ocultar la lista de reproducción
    PlaybackButton {
        id: playlistButton
        anchors.top: parent.top
        anchors.right: parent.right
        anchors.margins: 20
        width: 40
        height: 40
        iconSource: "file:qml/assets/playlist.svg"
        z: 10

        onClicked: {
            togglePlaylist()
            animatePulse()
        }
    }

    // Conexiones con el backend (se implementarán más tarde)
    Connections {
        target: musicPlayer  // Objeto expuesto desde Python

        function onSongChanged(title, artist, album, coverPath) {
            mainWindow.currentSong = title
            mainWindow.currentArtist = artist
            mainWindow.currentAlbum = album
            mainWindow.albumCoverSource = coverPath
        }

        function onPositionChanged(position, duration) {
            mainWindow.currentPosition = position
            mainWindow.totalDuration = duration
        }

        function onPlaybackStateChanged(playing) {
            mainWindow.isPlaying = playing
        }
    }

    // Función para alternar la visibilidad del panel de lista de reproducción
    function togglePlaylist() {
        playlistVisible = !playlistVisible
    }

    // Función para animar un pulso en un botón
    function animatePulse() {
        pulseAnimation.start()
    }

    // Animación de pulso para feedback visual
    SequentialAnimation {
        id: pulseAnimation

        PropertyAnimation {
            target: mainContainer
            property: "scale"
            from: 1.0
            to: 1.02
            duration: 100
            easing.type: Easing.OutQuad
        }

        PropertyAnimation {
            target: mainContainer
            property: "scale"
            from: 1.02
            to: 1.0
            duration: 100
            easing.type: Easing.InOutQuad
        }
    }

    // Conectar la señal de toggle playlist
    Component.onCompleted: {
        togglePlaylistRequested.connect(togglePlaylist)

        // Depuración de letras
        console.log("Main window completado. Modelo de letras:", lyricsModel ? "disponible" : "no disponible")
        if (lyricsModel) {
            console.log("Número de líneas en mainWindow:", lyricsModel.length)
        }
    }
}
