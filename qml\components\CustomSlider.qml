import QtQuick
import QtQuick.Controls
import QtQuick.Effects
import QtQuick.Layouts

Item {
    id: root
    height: 50

    // Propiedades personalizadas
    property color grooveColor: "#444444"
    property color progressColor: "#FFFFFF"
    property color handleColor: "#FFFFFF"
    property bool handleVisible: false
    property int handleSize: 16  // Tamaño fijo para el handle
    property int value: 0
    property int from: 0
    property int to: 100
    property real visualPosition: (value - from) / (to - from)
    property string timeElapsed: formatTime(value)
    property string timeTotal: formatTime(to)

    // Señal emitida cuando el usuario mueve el slider
    signal moved(int value)

    // Función para formatear el tiempo en formato mm:ss
    function formatTime(seconds) {
        var mins = Math.floor(seconds / 60)
        var secs = seconds % 60
        return mins.toString().padStart(2, '0') + ":" + secs.toString().padStart(2, '0')
    }

    // Layout principal
    ColumnLayout {
        anchors.fill: parent
        spacing: 2

        // Slider personalizado
        Item {
            Layout.fillWidth: true
            Layout.preferredHeight: 20

            // Barra de fondo
            Rectangle {
                id: groove
                anchors.centerIn: parent
                width: parent.width
                height: 6
                radius: 3
                color: root.grooveColor

                // Transición suave para el color
                Behavior on color {
                    ColorAnimation {
                        duration: 300
                        easing.type: Easing.InOutQuad
                    }
                }

                // Barra de progreso
                Rectangle {
                    id: progress
                    width: root.visualPosition * parent.width
                    height: parent.height
                    color: root.progressColor
                    radius: 3

                    // Animación de transición para el ancho
                    Behavior on width {
                        NumberAnimation {
                            duration: 100
                            easing.type: Easing.OutCubic
                        }
                    }

                    // Efecto de brillo en hover
                    states: [
                        State {
                            name: "hovered"
                            when: mouseArea.containsMouse
                            PropertyChanges {
                                target: progress
                                color: Qt.lighter(root.progressColor, 1.2)
                            }
                        }
                    ]

                    // Transición suave para el color
                    transitions: [
                        Transition {
                            ColorAnimation {
                                duration: 300
                                easing.type: Easing.InOutQuad
                            }
                        }
                    ]
                }
            }

            // Handle personalizado
            Rectangle {
                id: handle
                x: root.visualPosition * (parent.width - width)
                anchors.verticalCenter: groove.verticalCenter
                width: root.handleSize
                height: root.handleSize
                radius: width / 2
                color: root.handleColor
                opacity: root.handleVisible ? 1.0 : 0.0
                visible: opacity > 0

                // Efecto de sombra
                layer.enabled: true
                layer.effect: MultiEffect {
                    shadowEnabled: true
                    shadowHorizontalOffset: 0
                    shadowVerticalOffset: 2
                    shadowBlur: 5.0
                    shadowColor: "#40000000"
                }

                // Animación de transición para el tamaño
                Behavior on width {
                    NumberAnimation {
                        duration: 300
                        easing.type: Easing.InOutQuad
                    }
                }

                Behavior on height {
                    NumberAnimation {
                        duration: 300
                        easing.type: Easing.InOutQuad
                    }
                }

                // Animación de transición para la opacidad
                Behavior on opacity {
                    NumberAnimation {
                        duration: 300
                        easing.type: Easing.InOutQuad
                    }
                }
            }

            // Área para detectar interacciones del mouse
            MouseArea {
                id: mouseArea
                anchors.fill: parent
                hoverEnabled: true

                onPressed: function(mouse) {
                    updateValue(mouse)
                }

                onPositionChanged: function(mouse) {
                    if (pressed) {
                        updateValue(mouse)
                    }
                }

                onEntered: {
                    root.grooveColor = "#666666"
                    root.handleVisible = true
                    root.handleSize = 16
                }

                onExited: {
                    if (!pressed) {
                        root.grooveColor = "#444444"
                        root.handleVisible = false
                        // No cambiamos el tamaño aquí para permitir que la animación de opacidad funcione
                    }
                }

                onReleased: {
                    if (!containsMouse) {
                        root.grooveColor = "#444444"
                        root.handleVisible = false
                        // No cambiamos el tamaño aquí para permitir que la animación de opacidad funcione
                    }
                }

                // Función para actualizar el valor basado en la posición del mouse
                function updateValue(mouse) {
                    var pos = Math.max(0, Math.min(1, mouse.x / width))
                    var newValue = Math.round(pos * (root.to - root.from) + root.from)
                    if (newValue !== root.value) {
                        root.value = newValue
                        root.moved(newValue)
                    }
                }
            }
        }

        // Etiquetas de tiempo
        Row {
            Layout.fillWidth: true
            Layout.preferredHeight: 20

            // Tiempo transcurrido
            Text {
                width: parent.width / 2
                horizontalAlignment: Text.AlignLeft
                text: root.timeElapsed
                color: "#AAAAAA"
                font.pixelSize: 12
            }

            // Tiempo total
            Text {
                width: parent.width / 2
                horizontalAlignment: Text.AlignRight
                text: root.timeTotal
                color: "#AAAAAA"
                font.pixelSize: 12
            }
        }
    }

    // Animaciones para el groove
    Behavior on grooveColor {
        ColorAnimation {
            duration: 300
            easing.type: Easing.InOutQuad
        }
    }
}
