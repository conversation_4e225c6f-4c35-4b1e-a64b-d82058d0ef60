#!/usr/bin/env python3
"""
Reproductor de música con interfaz QML y animaciones.
"""

import sys
import logging
from pathlib import Path

from PySide6.QtCore import QObject, Signal, Slot, QUrl, QTimer
from PySide6.QtGui import QGuiApplication
from PySide6.QtQml import QQmlApplicationEngine
from PySide6.QtQuickControls2 import QQuickStyle

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

# Función para verificar los iconos disponibles
def check_available_icons(assets_dir):
    """Verifica los iconos disponibles en la carpeta de assets"""
    if not assets_dir.exists():
        return

    icons = list(assets_dir.glob("*.svg"))
    logging.info(f"Iconos disponibles: {len(icons)}")
    for icon in icons:
        logging.debug(f"  - {icon.name}")

# Clase puente para comunicar Python con QML
class MusicPlayerBridge(QObject):
    """Clase puente para comunicar el backend de Python con la interfaz QML"""

    # Señales para notificar cambios a QML
    songChanged = Signal(str, str, str, str)  # título, artista, álbum, ruta de imagen
    positionChanged = Signal(int, int)  # posición actual, duración total
    playbackStateChanged = Signal(bool)  # reproduciendo o pausado
    playlistChanged = Signal(list)  # lista de canciones

    def __init__(self, parent=None):
        super().__init__(parent)
        self._player = None  # Referencia al reproductor de música
        self._playlist = []  # Lista de reproducción
        self._current_index = -1  # Índice actual
        self._is_playing = False  # Estado de reproducción

        # Simulación de posición para pruebas
        self._position = 0
        self._duration = 100
        self._position_timer = QTimer(self)
        self._position_timer.setInterval(1000)  # Actualizar cada segundo
        self._position_timer.timeout.connect(self._update_position)

    def _update_position(self):
        """Actualiza la posición de reproducción (simulación)"""
        if self._is_playing and self._position < self._duration:
            self._position += 1
            self.positionChanged.emit(self._position, self._duration)
            logging.debug(f"Posición: {self._position}/{self._duration}")
        elif self._position >= self._duration:
            # Simular el final de la canción
            self._position = 0
            self.next()  # Avanzar a la siguiente canción automáticamente
            logging.info("Canción finalizada, avanzando a la siguiente")

    @Slot()
    def playPause(self):
        """Alterna entre reproducir y pausar"""
        self._is_playing = not self._is_playing
        self.playbackStateChanged.emit(self._is_playing)

        if self._is_playing:
            self._position_timer.start()
        else:
            self._position_timer.stop()

    @Slot()
    def next(self):
        """Avanza a la siguiente canción"""
        if len(self._playlist) > 0:
            self._current_index = (self._current_index + 1) % len(self._playlist)
            self._load_current_song()

    @Slot()
    def previous(self):
        """Retrocede a la canción anterior"""
        if len(self._playlist) > 0:
            self._current_index = (self._current_index - 1) % len(self._playlist)
            self._load_current_song()

    @Slot(int)
    def seek(self, position):
        """Busca una posición específica"""
        self._position = position
        self.positionChanged.emit(self._position, self._duration)

    @Slot(int)
    def selectSong(self, index):
        """Selecciona una canción por su índice"""
        if 0 <= index < len(self._playlist):
            self._current_index = index
            self._load_current_song()

    def _load_current_song(self):
        """Carga la canción actual"""
        if 0 <= self._current_index < len(self._playlist):
            song = self._playlist[self._current_index]
            self.songChanged.emit(
                song.get('title', 'Sin título'),
                song.get('artist', 'Desconocido'),
                song.get('album', 'Desconocido'),
                song.get('coverPath', '')
            )
            self._position = 0
            self._duration = song.get('durationSeconds', 100)
            self.positionChanged.emit(self._position, self._duration)

    def load_demo_playlist(self):
        """Carga una lista de reproducción de demostración"""
        self._playlist = [
            {
                'title': 'Shape of You',
                'artist': 'Ed Sheeran',
                'album': '÷ (Divide)',
                'coverPath': 'file:qml/assets/spotify.svg',
                'duration': '3:53',
                'durationSeconds': 233
            },
            {
                'title': 'Blinding Lights',
                'artist': 'The Weeknd',
                'album': 'After Hours',
                'coverPath': 'file:qml/assets/spotify.svg',
                'duration': '3:20',
                'durationSeconds': 200
            },
            {
                'title': 'Dance Monkey',
                'artist': 'Tones and I',
                'album': 'The Kids Are Coming',
                'coverPath': 'file:qml/assets/spotify.svg',
                'duration': '3:29',
                'durationSeconds': 209
            },
            {
                'title': 'Someone You Loved',
                'artist': 'Lewis Capaldi',
                'album': 'Divinely Uninspired to a Hellish Extent',
                'coverPath': 'file:qml/assets/spotify.svg',
                'duration': '3:02',
                'durationSeconds': 182
            },
            {
                'title': 'Bad Guy',
                'artist': 'Billie Eilish',
                'album': 'When We All Fall Asleep, Where Do We Go?',
                'coverPath': 'file:qml/assets/spotify.svg',
                'duration': '3:14',
                'durationSeconds': 194
            },
            {
                'title': 'Señorita',
                'artist': 'Shawn Mendes, Camila Cabello',
                'album': 'Señorita',
                'coverPath': 'file:qml/assets/spotify.svg',
                'duration': '3:10',
                'durationSeconds': 190
            }
        ]

        # Convertir a lista de diccionarios para asegurar compatibilidad con QML
        playlist_data = []
        for song in self._playlist:
            playlist_data.append(dict(song))

        self._playlist = playlist_data
        logging.info(f"Lista de reproducción cargada con {len(self._playlist)} canciones")
        self.playlistChanged.emit(self._playlist)

        # Cargar la primera canción
        if len(self._playlist) > 0:
            self._current_index = 0
            self._load_current_song()


def main():
    """Función principal"""
    # Crear la aplicación
    app = QGuiApplication(sys.argv)
    app.setApplicationName("Reproductor de Música QML")
    app.setOrganizationName("MiOrganización")

    # Establecer el estilo
    QQuickStyle.setStyle("Fusion")

    # Crear el motor QML
    engine = QQmlApplicationEngine()

    # Crear el puente entre Python y QML
    bridge = MusicPlayerBridge()

    # Cargar datos de demostración
    bridge.load_demo_playlist()

    # Crear una copia de la lista de reproducción para evitar problemas de referencia
    playlist_data = []
    for song in bridge._playlist:
        playlist_data.append(dict(song))

    # Registrar el modelo de la lista de reproducción
    engine.rootContext().setContextProperty("playlistModel", playlist_data)
    logging.info(f"Modelo de lista de reproducción establecido: {len(playlist_data)} canciones")

    # Registrar el índice actual
    engine.rootContext().setContextProperty("currentSongIndex", bridge._current_index)

    # Letras de ejemplo por defecto
    lyrics = [
        "Bienvenido al reproductor de música",
        "Esta es una canción de ejemplo",
        "Con letras para mostrar la funcionalidad de karaoke",
        "Puedes hacer clic en una línea para seleccionarla",
        "Las letras se sincronizarán con la música",
        "A medida que avanza la canción",
        "Las palabras se iluminarán",
        "Para que puedas seguir el ritmo",
        "Y cantar junto con la música",
        "¡Disfruta de la experiencia de karaoke!",
        "Estas letras son solo un ejemplo",
        "Pero puedes cargar tus propias letras",
        "Desde archivos de texto o LRC",
        "Con marcas de tiempo para sincronización",
        "La interfaz se adaptará automáticamente",
        "Para mostrar las letras de forma atractiva",
        "Con efectos visuales y animaciones",
        "Que hacen que la experiencia sea más inmersiva",
        "Esperamos que disfrutes de esta función",
        "Y que te diviertas cantando tus canciones favoritas"
    ]

    # Cargar letras desde archivo si existe
    try:
        lyrics_file = Path(__file__).parent / "src" / "ui" / "lyrics.txt"
        if lyrics_file.exists():
            with open(lyrics_file, encoding='utf-8') as f:
                file_lyrics = [line.strip() for line in f if line.strip()]
                if file_lyrics:
                    lyrics = file_lyrics
    except Exception as e:
        logging.warning(f"No se pudieron cargar las letras desde archivo: {e}")

    logging.info(f"Letras cargadas: {len(lyrics)} líneas")

    # Configurar controlador de errores para QML
    def handle_qml_warning(message):
        logging.warning(f"QML Warning: {message}")

    engine.warnings.connect(handle_qml_warning)

    # Verificar que la carpeta de assets existe
    assets_dir = Path(__file__).parent / "qml" / "assets"
    if not assets_dir.exists():
        logging.warning(f"La carpeta de assets no existe: {assets_dir}")
    else:
        logging.info(f"Usando iconos de la carpeta: {assets_dir}")
        check_available_icons(assets_dir)

    # Obtener la ruta al directorio QML
    qml_dir = Path(__file__).parent / "qml"

    # Agregar la ruta de importación para los componentes QML
    engine.addImportPath(str(qml_dir))

    # Registrar el puente como contexto
    engine.rootContext().setContextProperty("musicPlayer", bridge)

    # Ruta al archivo QML principal
    main_qml = qml_dir / "main.qml"

    # Cargar el archivo QML principal
    engine.load(QUrl.fromLocalFile(str(main_qml)))

    # Verificar que se cargó correctamente
    if not engine.rootObjects():
        sys.exit(-1)

    # Obtener el objeto raíz
    root_object = engine.rootObjects()[0]

    # Establecer el modelo de letras en el objeto mainWindow
    if root_object:
        # En PySide6, la conversión entre tipos de Python y tipos de Qt se maneja automáticamente
        root_object.setProperty("lyricsModel", lyrics)
        logging.info(f"Modelo de letras establecido en mainWindow: {len(lyrics)} líneas")

    # Establecer el modelo de lista de reproducción en el objeto mainWindow
    if root_object and playlist_data:
        # En PySide6, la conversión entre tipos de Python y tipos de Qt se maneja automáticamente
        root_object.setProperty("playlistData", playlist_data)
        logging.info(f"Modelo de lista de reproducción establecido en mainWindow: {len(playlist_data)} canciones")

    # Ejecutar la aplicación
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
