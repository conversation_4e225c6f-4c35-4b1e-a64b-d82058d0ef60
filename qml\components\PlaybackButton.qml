import QtQuick
import QtQuick.Controls
import QtQuick.Effects

Item {
    id: root
    width: 50
    height: 50

    // Propiedades
    property string iconSource: ""
    property string iconColor: "#FFFFFF"
    property string iconHoverColor: "#CCCCCC"
    property string bgColor: "#333333"
    property bool toggled: false
    property real bgOpacity: toggled ? 1.0 : 0.0
    property real scale: 1.0

    // Se<PERSON><PERSON>
    signal clicked()

    // Rectángulo de fondo
    Rectangle {
        id: background
        anchors.fill: parent
        radius: width / 2
        color: root.bgColor
        opacity: root.bgOpacity

        // Animación de transición
        Behavior on opacity {
            NumberAnimation {
                duration: 260
                easing.type: Easing.InOutCubic
            }
        }
    }

    // Icono
    Image {
        id: icon
        anchors.centerIn: parent
        width: parent.width * 0.6
        height: parent.height * 0.6
        source: root.iconSource
        sourceSize.width: width * 2  // Para mejor calidad
        sourceSize.height: height * 2
        smooth: true
        antialiasing: true
        cache: true
        asynchronous: true

        // Estado de carga
        onStatusChanged: {
            if (status === Image.Error) {
                console.error("Error al cargar el icono:", source)
                // Intentar cargar un icono de respaldo
                source = "file:qml/assets/music.svg"
            } else if (status === Image.Ready) {
                console.log("Icono cargado correctamente:", source)
            }
        }

        // Colorear el icono
        layer.enabled: true
        layer.effect: MultiEffect {
            colorization: 1.0
            colorizationColor: mouseArea.containsMouse ? root.iconHoverColor : root.iconColor

            // Animación de transición
            Behavior on colorizationColor {
                ColorAnimation {
                    duration: 260
                    easing.type: Easing.InOutCubic
                }
            }
        }
    }

    // Efecto de escala
    transform: Scale {
        origin.x: root.width / 2
        origin.y: root.height / 2
        xScale: root.scale
        yScale: root.scale
    }

    // Animación de escala
    Behavior on scale {
        NumberAnimation {
            duration: 200
            easing.type: Easing.OutCubic
        }
    }

    // Área para detectar interacciones del mouse
    MouseArea {
        id: mouseArea
        anchors.fill: parent
        hoverEnabled: true

        onClicked: {
            clickAnimation.start()
            root.clicked()
        }

        onEntered: {
            root.bgOpacity = 0.3
        }

        onExited: {
            if (!root.toggled) {
                root.bgOpacity = 0.0
            }
        }
    }

    // Animación al hacer clic
    SequentialAnimation {
        id: clickAnimation

        NumberAnimation {
            target: root
            property: "scale"
            from: 1.0
            to: 0.9
            duration: 100
            easing.type: Easing.OutCubic
        }

        NumberAnimation {
            target: root
            property: "scale"
            from: 0.9
            to: 1.0
            duration: 100
            easing.type: Easing.OutCubic
        }
    }

    // Función para animar un "pulso" (como un latido)
    function animatePulse() {
        pulseAnimation.start()
    }

    // Animación de pulso
    SequentialAnimation {
        id: pulseAnimation

        NumberAnimation {
            target: root
            property: "scale"
            from: 1.0
            to: 1.18
            duration: 150
            easing.type: Easing.OutCubic
        }

        NumberAnimation {
            target: root
            property: "scale"
            from: 1.18
            to: 0.98
            duration: 150
            easing.type: Easing.InOutCubic
        }

        NumberAnimation {
            target: root
            property: "scale"
            from: 0.98
            to: 1.04
            duration: 120
            easing.type: Easing.OutCubic
        }

        NumberAnimation {
            target: root
            property: "scale"
            from: 1.04
            to: 1.0
            duration: 100
            easing.type: Easing.InOutCubic
        }
    }

    // Función para alternar el estado
    function toggle() {
        root.toggled = !root.toggled
        root.bgOpacity = root.toggled ? 1.0 : 0.0
    }
}
