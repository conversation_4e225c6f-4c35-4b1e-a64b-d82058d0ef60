pragma Singleton
import QtQuick 2.15

QtObject {
    // Colores principales
    readonly property color backgroundColor: "#121212"
    readonly property color surfaceColor: "#1E1E1E"
    readonly property color primaryColor: "#41CD52"
    readonly property color secondaryColor: "#1DB954"
    readonly property color textColor: "#FFFFFF"
    readonly property color secondaryTextColor: "#CCCCCC"
    readonly property color disabledTextColor: "#999999"
    
    // Colores de estado
    readonly property color hoverColor: "#333333"
    readonly property color pressedColor: "#444444"
    readonly property color selectedColor: "#333333"
    
    // Tamaños de fuente
    readonly property int fontSizeLarge: 20
    readonly property int fontSizeMedium: 16
    readonly property int fontSizeSmall: 14
    readonly property int fontSizeExtraSmall: 12
    
    // Radios de bordes
    readonly property int radiusLarge: 20
    readonly property int radiusMedium: 14
    readonly property int radiusSmall: 8
    readonly property int radiusExtraSmall: 4
    
    // Duración de animaciones
    readonly property int animationDurationFast: 150
    readonly property int animationDurationMedium: 250
    readonly property int animationDurationSlow: 350
    
    // Curvas de animación
    readonly property int easingTypeStandard: Easing.OutCubic
    readonly property int easingTypeAccelerate: Easing.InCubic
    readonly property int easingTypeDecelerate: Easing.OutCubic
    readonly property int easingTypeSmooth: Easing.InOutCubic
    
    // Sombras
    readonly property color shadowColor: "#40000000"
    readonly property int shadowRadius: 10
    readonly property int shadowSamples: 21
    
    // Espaciado
    readonly property int spacingSmall: 5
    readonly property int spacingMedium: 10
    readonly property int spacingLarge: 20
    
    // Tamaños de componentes
    readonly property int buttonSizeSmall: 36
    readonly property int buttonSizeMedium: 48
    readonly property int buttonSizeLarge: 60
    
    // Opacidades
    readonly property real opacityDisabled: 0.5
    readonly property real opacityHint: 0.7
    readonly property real opacityFull: 1.0
}
