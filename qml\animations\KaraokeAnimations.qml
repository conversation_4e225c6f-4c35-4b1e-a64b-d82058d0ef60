import QtQuick
import QtQuick.Controls
import QtQuick.Effects
import QtQuick.Shapes

Item {
    id: root

    // Propiedades para configurar las animaciones
    property var lyricsModel: []           // Modelo de datos con las letras
    property int currentIndex: 0           // Índice de la línea actual
    property real progress: 0.0            // Progreso de la línea actual (0.0 - 1.0)
    property bool mouseHoverEnabled: true  // Habilitar efectos de hover
    property point mousePosition: Qt.point(0, 0)  // Posición actual del mouse

    // Propiedad para indicar si la carátula del álbum está expandida
    property bool albumCoverExpanded: false

    // Observar cambios en el estado de la carátula
    onAlbumCoverExpandedChanged: {
        // Forzar la actualización del modelo para ajustar el número de líneas visibles
        lyricsView.model = lyricsView.model

        // Asegurar que la línea activa permanezca centrada
        if (currentIndex >= 0 && currentIndex < lyricsView.count) {
            lyricsView.positionViewAtIndex(currentIndex, ListView.Center)
        }
    }

    // Referencias a componentes internos (para acceso desde fuera)
    property ScrollBar scrollBar: verticalScrollBar

    // Propiedades de estilo
    property color activeColor: "#ffffff"
    property color inactiveColor: "#aaaaaa"
    property color highlightColor: "#ffffff"
    property real maxBlurRadius: 8
    property real maxGlowRadius: 8
    property real maxScale: 1.0
    property real minScale: 0.75
    property string fontFamily: "Segoe UI, Arial, sans-serif"
    property int activeFontSize: 28
    property int inactiveFontSize: 20
    property int fontWeight: Font.Medium

    // Propiedades de animación
    property int transitionDuration: 400
    property int secondaryTransitionDuration: 100 // Duración específica para la línea secundaria
    property int hoverTransitionDuration: 350
    property int wordProgressDuration: 200

    // Señales
    signal lineClicked(int index)

    // Componente para una línea de karaoke
    Component {
        id: lyricLineDelegate

        Item {
            id: lineItem
            width: ListView.view.width
            height: root.albumCoverExpanded ? 28 : 34  // Altura dinámica según el estado de la carátula

            // Animación suave para la transición de altura
            Behavior on height {
                NumberAnimation {
                    duration: 350
                    easing.type: Easing.OutCubic
                }
            }

            // Propiedades calculadas
            property bool isCurrent: index === root.currentIndex
            property real distanceFromCurrent: Math.abs(index - root.currentIndex)
            property real normalizedDistance: Math.min(1.0, distanceFromCurrent / 5)

            // Propiedades para el sistema de línea activa secundaria
            property bool isSecondaryActive: false
            property bool isNearPrimary: false  // Indica si la línea secundaria está cerca de la línea principal
            property real distanceFromSecondary: 0
            property bool scrollbarActive: ListView.view ? ListView.view.isScrollbarActive : false

            // Actualizar la distancia desde la línea secundaria activa
            function updateSecondaryDistance(secondaryIndex) {
                if (secondaryIndex >= 0) {
                    // Calcular la distancia real
                    distanceFromSecondary = Math.abs(index - secondaryIndex);

                    // Si esta línea es la primaria activa, ya no forzamos una distancia alta
                    // para permitir que la secundaria y primaria interactúen cuando están cerca
                    if (isCurrent) {
                        // Mantener un valor pequeño para que la línea primaria siga teniendo prioridad
                        // pero permitiendo la interacción con la secundaria
                        distanceFromSecondary = 0.5;
                    }
                } else {
                    distanceFromSecondary = 999; // Valor alto si no hay línea secundaria
                }
            }

            // Valores de estilo basados en la distancia con transición más suave
            property real opacityValue: {
                // Línea activa principal (playback) - máxima opacidad
                if (isCurrent) return 1.0;

                // Línea activa secundaria (scrollbar) - alta opacidad
                if (isSecondaryActive) return 0.95;

                // Calcular opacidad basada en la distancia a la línea primaria
                // (la secundaria ya no afecta a las líneas normales)
                var primaryDistance = distanceFromCurrent;

                // Opacidad mejorada para asegurar que todas las líneas sean visibles
                if (primaryDistance <= 1) {
                    // Las líneas inmediatamente adyacentes a la principal tienen alta opacidad
                    return 0.95;
                } else if (primaryDistance <= 2) {
                    // Opacidad alta para las líneas a 2 posiciones de distancia
                    return 0.9;
                } else if (primaryDistance <= 3) {
                    // Opacidad moderada para las líneas a 3 posiciones de distancia
                    return 0.85;
                } else if (primaryDistance <= 4) {
                    // Opacidad reducida para las líneas a 4 posiciones de distancia
                    return 0.8;
                } else {
                    // Opacidad mínima más alta para líneas distantes
                    return Math.max(0.5, 0.8 - ((primaryDistance - 4) * 0.05));
                }
            }

            property real scaleValue: {
                // Línea activa principal (playback) - escala completa
                if (isCurrent) return 1.0;

                // Línea activa secundaria (scrollbar) - escala casi completa
                if (isSecondaryActive) return 0.98;

                // Calcular escala basada en la distancia a la línea primaria
                // (la secundaria ya no afecta a las líneas normales)
                var primaryDistance = distanceFromCurrent;

                // Escala ajustada para coincidir con el efecto de desenfoque gradual
                if (primaryDistance <= 1) {
                    // Las líneas inmediatamente adyacentes a la principal tienen escala casi completa
                    return 0.95;
                } else if (primaryDistance <= 2) {
                    // Escala ligeramente reducida para las líneas a 2 posiciones de distancia
                    return 0.9;
                } else if (primaryDistance <= 3) {
                    // Escala moderadamente reducida para las líneas a 3 posiciones de distancia
                    return 0.85;
                } else if (primaryDistance <= 4) {
                    // Escala más reducida para las líneas a 4 posiciones de distancia
                    return 0.8;
                } else {
                    // Reducción más pronunciada para líneas más distantes
                    return Math.max(root.minScale, 0.8 - ((primaryDistance - 4) * 0.05));
                }
            }

            property real blurValue: {
                // Línea activa principal (playback) - sin desenfoque
                if (isCurrent) return 0.0;

                // Línea activa secundaria (scrollbar) - sin desenfoque
                if (isSecondaryActive) return 0.0;

                // Calcular desenfoque basado en la distancia a ambas líneas activas
                var primaryDistance = distanceFromCurrent;
                var secondaryDistance = distanceFromSecondary;

                // Verificar si la línea secundaria está activa y no está cerca de la principal
                var useSecondaryEffect = scrollbarActive && !isNearPrimary;

                // Calcular la distancia entre las líneas activas primaria y secundaria
                var distanceBetweenActiveLines = Math.abs(root.currentIndex - secondaryDistance);

                // Calcular el desenfoque basado en la línea primaria
                var primaryBlur;
                if (primaryDistance <= 1) {
                    // Las líneas inmediatamente adyacentes a la principal permanecen nítidas
                    primaryBlur = 0.0;
                } else if (primaryDistance <= 2) {
                    // Desenfoque casi imperceptible para las líneas a 2 posiciones de distancia
                    primaryBlur = 0.2;
                } else if (primaryDistance <= 3) {
                    // Desenfoque muy sutil para las líneas a 3 posiciones de distancia
                    primaryBlur = 0.5;
                } else if (primaryDistance <= 4) {
                    // Desenfoque ligero para las líneas a 4 posiciones de distancia
                    primaryBlur = 1.0;
                } else {
                    // Desenfoque moderado para líneas más distantes (nunca excesivo)
                    primaryBlur = Math.min(3.0, 1.0 + ((primaryDistance - 4) * 0.5));
                }

                // Calcular el desenfoque basado en la línea secundaria solo si está activa y no está cerca de la principal
                var secondaryBlur = 999; // Valor alto por defecto para que no afecte
                if (useSecondaryEffect) {
                    if (secondaryDistance <= 1) {
                        // Las líneas inmediatamente adyacentes a la secundaria tienen desenfoque mínimo
                        secondaryBlur = 0.05;
                    } else if (secondaryDistance <= 2) {
                        // Desenfoque muy sutil para las líneas a 2 posiciones de distancia
                        secondaryBlur = 0.15;
                    } else if (secondaryDistance <= 3) {
                        // Desenfoque sutil para las líneas a 3 posiciones de distancia
                        secondaryBlur = 0.35;
                    } else if (secondaryDistance <= 4) {
                        // Desenfoque moderado para las líneas a 4 posiciones de distancia
                        secondaryBlur = 0.6;
                    } else {
                        // Desenfoque para líneas más distantes (reducido a la mitad)
                        secondaryBlur = Math.min(1.5, 0.6 + ((secondaryDistance - 4) * 0.25));
                    }
                }

                // Si las líneas activas están cerca una de la otra, reducir el efecto de desenfoque
                // para las líneas que están entre ellas
                var isBetweenActiveLines = useSecondaryEffect &&
                                          (index > Math.min(root.currentIndex, secondaryDistance) &&
                                           index < Math.max(root.currentIndex, secondaryDistance));

                // Calcular un factor de reducción basado en la proximidad entre las líneas activas
                var reductionFactor = 1.0;
                if (isBetweenActiveLines && distanceBetweenActiveLines <= 5) {
                    // Reducir el desenfoque para las líneas entre las activas cuando están cerca
                    reductionFactor = Math.max(0.3, distanceBetweenActiveLines / 5);
                }

                // Usar el valor de desenfoque más bajo entre primario y secundario
                // multiplicado por el factor de reducción
                return Math.min(primaryBlur, secondaryBlur) * reductionFactor;
            }

            // Efecto de resplandor: más intenso para la línea primaria, mejorado para la secundaria
            property real glowValue: {
                // Línea activa principal (playback) - resplandor más intenso
                if (isCurrent) return 0.7;

                // Línea activa secundaria (scrollbar) - resplandor mejorado
                if (isSecondaryActive && !isNearPrimary) {
                    // Solo aplicar resplandor si no está cerca de la línea principal
                    // Resplandor normal para la línea secundaria cuando está lejos de la primaria
                    return 0.4;
                }

                // Líneas normales o línea secundaria cerca de la principal - sin resplandor
                return 0.0;
            }

            // Contenedor para palabras individuales
            Row {
                id: wordsContainer
                anchors.centerIn: parent
                spacing: 5

                // Dividir la línea en palabras
                property var words: modelData ? modelData.split(" ") : []
                property int activeWordCount: isCurrent ? Math.ceil(words.length * root.progress) : 0

                // Crear componentes de texto para cada palabra
                Repeater {
                    model: wordsContainer.words

                    Text {
                        id: wordText
                        text: modelData + " "
                        font.pixelSize: lineItem.isCurrent ? root.activeFontSize :
                                       (lineItem.isSecondaryActive ? (root.activeFontSize * 0.8) : root.inactiveFontSize)
                        font.family: root.fontFamily
                        font.weight: root.fontWeight
                        color: {
                            if (lineItem.isCurrent) {
                                // Línea activa principal (playback)
                                return index < wordsContainer.activeWordCount ? root.activeColor : root.inactiveColor;
                            } else if (lineItem.isSecondaryActive && !lineItem.isNearPrimary) {
                                // Línea activa secundaria (scrollbar) - Color más distintivo
                                // Solo aplicar el color distintivo si no está cerca de la línea principal
                                return Qt.rgba(
                                    0.59,  // Azul desaturado
                                    0.71,  // Verde suave
                                    0.78,  // Azul grisáceo
                                    0.93   // Alta opacidad
                                );
                            } else {
                                // Líneas normales o línea secundaria cerca de la principal
                                return Qt.rgba(
                                    root.inactiveColor.r,
                                    root.inactiveColor.g,
                                    root.inactiveColor.b,
                                    lineItem.opacityValue
                                );
                            }
                        }

                        // Animación de color
                        Behavior on color {
                            ColorAnimation {
                                duration: 1800
                                easing.type: Easing.OutCubic
                            }
                        }
                    }
                }

                // Transformaciones
                transform: [
                    // Transformación de escala
                    Scale {
                        origin.x: wordsContainer.width/2
                        origin.y: wordsContainer.height/2
                        xScale: lineItem.scaleValue
                        yScale: lineItem.scaleValue
                    },
                    // Transformación vertical con distribución más natural
                    Translate {
                        y: {
                            // Distribución ajustada para el espaciado original de 12px
                            if (lineItem.distanceFromCurrent < 0) {
                                // Líneas por encima de la actual - distribución lineal
                                if (Math.abs(lineItem.distanceFromCurrent) <= 4) {
                                    // Primeras 4 líneas arriba con espaciado uniforme
                                    // Espaciado base de 8px más el espaciado natural de 12px
                                    return -Math.abs(lineItem.distanceFromCurrent) * 8;
                                } else {
                                    // Líneas más distantes con mayor separación
                                    return -(32 + (Math.abs(lineItem.distanceFromCurrent) - 4) * 12);
                                }
                            } else if (lineItem.distanceFromCurrent > 0) {
                                // Líneas por debajo de la actual - distribución lineal
                                if (lineItem.distanceFromCurrent <= 4) {
                                    // Primeras 4 líneas abajo con espaciado uniforme
                                    // Espaciado base de 8px más el espaciado natural de 12px
                                    return lineItem.distanceFromCurrent * 8;
                                } else {
                                    // Líneas más distantes con mayor separación
                                    return 32 + ((lineItem.distanceFromCurrent - 4) * 12);
                                }
                            }
                            return 0;
                        }
                    }
                ]

                // Efecto de desenfoque
                layer.enabled: lineItem.blurValue > 0
                layer.effect: MultiEffect {
                    blurEnabled: true
                    blur: lineItem.blurValue
                }
            }

            // Efecto de resplandor para la línea actual
            Item {
                id: glowEffect
                anchors.fill: wordsContainer
                opacity: lineItem.glowValue

                // Duplicar el contenido para crear el efecto de resplandor SOLO en la línea principal activa
                Rectangle {
                    anchors.fill: parent
                    color: "transparent"
                    visible: lineItem.isCurrent // Solo mostrar el glow para la línea principal

                    // Clonar el contenido de wordsContainer
                    Row {
                        id: glowContainer
                        anchors.centerIn: parent
                        spacing: 5

                        // Crear componentes de texto para cada palabra
                        Repeater {
                            model: wordsContainer.words

                            Text {
                                text: modelData + " "
                                font.pixelSize: root.activeFontSize
                                font.family: root.fontFamily
                                font.weight: root.fontWeight
                                color: root.highlightColor

                                // Efecto de resplandor
                                layer.enabled: true
                                layer.effect: MultiEffect {
                                    shadowEnabled: true
                                    shadowHorizontalOffset: 0
                                    shadowVerticalOffset: 0
                                    shadowColor: root.highlightColor
                                    shadowBlur: root.maxGlowRadius
                                }
                            }
                        }
                    }
                }

                Behavior on opacity {
                    NumberAnimation {
                        duration: root.transitionDuration
                        easing.type: Easing.OutCubic
                    }
                }
            }

            // Animaciones suaves para transiciones
            Behavior on opacityValue {
                NumberAnimation {
                    duration: lineItem.isSecondaryActive ? root.secondaryTransitionDuration : root.transitionDuration
                    easing.type: Easing.OutCubic
                }
            }

            Behavior on scaleValue {
                NumberAnimation {
                    duration: lineItem.isSecondaryActive ? root.secondaryTransitionDuration : root.transitionDuration
                    easing.type: Easing.OutCubic
                }
            }

            Behavior on blurValue {
                NumberAnimation {
                    duration: lineItem.isSecondaryActive ? root.secondaryTransitionDuration : 900
                    easing.type: Easing.OutCubic
                }
            }

            // Área para detectar clics
            MouseArea {
                anchors.fill: parent

                // Evitar que el MouseArea propague eventos de arrastre
                drag.target: null
                preventStealing: true

                onClicked: {
                    root.lineClicked(index);
                }
            }
        }
    }

    // Área para detectar interacción con el componente de letras
    MouseArea {
        id: lyricsAreaMouseArea
        anchors.fill: parent
        hoverEnabled: true
        propagateComposedEvents: true

        // Mostrar la barra de desplazamiento al entrar en el área
        onEntered: {
            if (lyricsView.contentHeight > lyricsView.height) {
                verticalScrollBar.active = true
                scrollBarHideTimer.stop()
            }
        }

        onExited: {
            if (!verticalScrollBar.isDragging) {
                scrollBarHideTimer.restart()
            }
        }

        // Reiniciar el temporizador al mover el mouse
        onPositionChanged: {
            if (lyricsView.contentHeight > lyricsView.height) {
                verticalScrollBar.active = true
                if (!verticalScrollBar.isDragging) {
                    scrollBarHideTimer.restart()
                }
            }
        }

        // Permitir que los eventos pasen a la vista de lista
        onPressed: function(mouse) { mouse.accepted = false }
        onReleased: function(mouse) { mouse.accepted = false }
        onClicked: function(mouse) { mouse.accepted = false }
        onWheel: function(wheel) {
            wheel.accepted = false
            // Mostrar la barra de desplazamiento al usar la rueda del mouse
            if (lyricsView.contentHeight > lyricsView.height) {
                verticalScrollBar.active = true
                scrollBarHideTimer.restart()
                // Activar la línea secundaria cuando se usa la rueda del mouse
                lyricsView.isScrollbarActive = true
                // Detener el temporizador de redirección cuando el usuario interactúa
                secondaryLineRedirectTimer.stop()
                // Detener cualquier animación de redirección en curso
                lyricsView.stopScrollAnimation()
                // Actualizar la línea activa secundaria después de un breve retraso
                Qt.callLater(lyricsView.updateSecondaryActiveLine)
            }
        }
    }

    // Vista de lista para mostrar las letras
    ListView {
        id: lyricsView
        anchors.fill: parent
        anchors.margins: root.albumCoverExpanded ? 5 : 10  // Márgenes dinámicos según el estado de la carátula

        // Animación suave para la transición de márgenes
        Behavior on anchors.margins {
            NumberAnimation {
                duration: 350
                easing.type: Easing.OutCubic
            }
        }
        // Asegurar que siempre haya el número adecuado de líneas para mostrar según el estado de la carátula
        model: {
            // Determinar el número de líneas a mostrar según el estado de la carátula
            var targetLines = root.albumCoverExpanded ? 6 : 9;

            if (root.lyricsModel && root.lyricsModel.length > 0) {
                // Si hay menos líneas que las necesarias, agregar líneas en blanco para completar
                if (root.lyricsModel.length < targetLines) {
                    var paddedModel = [...root.lyricsModel];
                    // Agregar líneas en blanco al principio y al final
                    var paddingNeeded = targetLines - paddedModel.length;
                    var paddingBefore = Math.floor(paddingNeeded / 2);
                    var paddingAfter = paddingNeeded - paddingBefore;

                    for (var i = 0; i < paddingBefore; i++) {
                        paddedModel.unshift("");  // Agregar al principio
                    }

                    for (var j = 0; j < paddingAfter; j++) {
                        paddedModel.push("");  // Agregar al final
                    }

                    return paddedModel;
                }
                return root.lyricsModel;
            } else {
                // Si no hay letras, mostrar líneas de ejemplo según el estado de la carátula
                if (root.albumCoverExpanded) {
                    // 6 líneas cuando la carátula está expandida
                    return [
                        "",
                        "",
                        "No hay letras disponibles",
                        "para esta canción",
                        "",
                        ""
                    ];
                } else {
                    // 9 líneas cuando la carátula está contraída
                    return [
                        "",
                        "",
                        "",
                        "No hay letras disponibles",
                        "para esta canción",
                        "",
                        "",
                        "",
                        ""
                    ];
                }
            }
        }
        delegate: lyricLineDelegate
        spacing: albumCoverExpanded ? 8 : 14  // Espaciado dinámico según el estado de la carátula

        // Animación suave para la transición del espaciado
        Behavior on spacing {
            NumberAnimation {
                duration: 350
                easing.type: Easing.OutCubic
            }
        }

        clip: true

        // Propiedades de estilo basadas en lyrics.css
        property bool isSynchronized: true

        // Propiedad para controlar si la línea secundaria debe ser visible
        property bool isScrollbarActive: false

        // Efecto de desvanecimiento mejorado en los bordes usando rectángulos con gradientes
        Rectangle {
            id: topFade
            anchors.top: parent.top
            anchors.left: parent.left
            anchors.right: parent.right
            height: parent.height * 0.08  // Minimized fade height to ensure all 9 lines are visible
            z: 1
            color: "transparent"

            gradient: Gradient {
                // Gradiente más sutil para mantener visibles todas las líneas
                GradientStop { position: 0.0; color: Qt.rgba(30/255, 30/255, 30/255, 0.4) }
                GradientStop { position: 0.3; color: Qt.rgba(30/255, 30/255, 30/255, 0.2) }
                GradientStop { position: 0.7; color: Qt.rgba(30/255, 30/255, 30/255, 0.1) }
                GradientStop { position: 1.0; color: "transparent" }
            }
        }

        Rectangle {
            id: bottomFade
            anchors.bottom: parent.bottom
            anchors.left: parent.left
            anchors.right: parent.right
            height: parent.height * 0.08  // Minimized fade height to ensure all 9 lines are visible
            z: 1
            color: "transparent"

            gradient: Gradient {
                // Gradiente más sutil para mantener visibles todas las líneas
                GradientStop { position: 0.0; color: "transparent" }
                GradientStop { position: 0.3; color: Qt.rgba(30/255, 30/255, 30/255, 0.1) }
                GradientStop { position: 0.7; color: Qt.rgba(30/255, 30/255, 30/255, 0.2) }
                GradientStop { position: 1.0; color: Qt.rgba(30/255, 30/255, 30/255, 0.4) }
            }
        }

        // Habilitar el desplazamiento interactivo
        interactive: true

        // Barra de desplazamiento vertical
        ScrollBar.vertical: ScrollBar {
            id: verticalScrollBar
            active: false
            visible: lyricsView.contentHeight > lyricsView.height

            // Propiedades para controlar el estado de arrastre
            property bool isDragging: false

            // Temporizador para ocultar la barra de desplazamiento después de 3 segundos
            Timer {
                id: scrollBarHideTimer
                interval: 3000
                running: false
                repeat: false
                onTriggered: {
                    if (!verticalScrollBar.isDragging) {
                        verticalScrollBar.active = false
                        // Desactivar la línea secundaria cuando se oculta la barra de desplazamiento
                        lyricsView.isScrollbarActive = false
                        // Actualizar la línea activa secundaria para reflejar el cambio
                        lyricsView.updateSecondaryActiveLine()
                        // Iniciar el temporizador para redirigir la línea secundaria
                        secondaryLineRedirectTimer.restart()
                    }
                }
            }

            // Temporizador para redirigir la línea secundaria a la línea principal después de 3 segundos
            Timer {
                id: secondaryLineRedirectTimer
                interval: 3000
                running: false
                repeat: false
                onTriggered: {
                    if (!verticalScrollBar.isDragging) {
                        // Redirigir la línea secundaria a la línea principal
                        lyricsView.redirectSecondaryLine()
                    }
                }
            }

            // Animación de desvanecimiento
            opacity: active ? 1.0 : 0.0
            Behavior on opacity {
                NumberAnimation {
                    duration: 500
                    easing.type: Easing.OutCubic
                }
            }

            // Personalización del estilo
            contentItem: Rectangle {
                implicitWidth: 6
                implicitHeight: 6
                radius: width / 2
                color: "#E0E0E0"  // Color más claro para mejor contraste
                opacity: 0.7      // Transparencia parcial para un estilo más moderno
            }

            // Ocultar completamente el fondo del scrollbar
            background: Item {
                // Componente vacío para eliminar el fondo visual
                implicitWidth: 6
            }

            // Detectar cuando el usuario comienza a arrastrar
            onPressedChanged: {
                if (pressed) {
                    isDragging = true
                    active = true
                    // Activar la línea secundaria cuando se usa la barra de desplazamiento
                    lyricsView.isScrollbarActive = true
                    // Detener el temporizador de redirección cuando el usuario interactúa
                    secondaryLineRedirectTimer.stop()
                    // Detener cualquier animación de redirección en curso
                    lyricsView.stopScrollAnimation()
                } else {
                    isDragging = false
                    scrollBarHideTimer.restart()
                    // Actualizar la línea activa secundaria cuando se suelta
                    lyricsView.updateSecondaryActiveLine()
                    // La línea secundaria seguirá visible hasta que se oculte la barra de desplazamiento
                }
            }
        }

        // Detectar interacción con la vista para mostrar la barra de desplazamiento
        onMovementStarted: {
            verticalScrollBar.active = true
            scrollBarHideTimer.stop()
            secondaryLineRedirectTimer.stop()
            // Activar la línea secundaria cuando se desplaza la vista
            isScrollbarActive = true
            // Detener cualquier animación de redirección en curso
            stopScrollAnimation()
            updateSecondaryActiveLine()
        }

        onMovementEnded: {
            if (!verticalScrollBar.isDragging) {
                scrollBarHideTimer.restart()
            }
            updateSecondaryActiveLine()
        }

        onFlickStarted: {
            verticalScrollBar.active = true
            scrollBarHideTimer.stop()
        }

        onFlickEnded: {
            if (!verticalScrollBar.isDragging) {
                scrollBarHideTimer.restart()
            }
            updateSecondaryActiveLine()
        }

        // Actualizar la línea activa secundaria durante el desplazamiento
        onContentYChanged: {
            // Solo actualizar durante el desplazamiento activo para evitar actualizaciones innecesarias
            if (moving || dragging || verticalScrollBar.isDragging) {
                // Usar un temporizador para limitar la frecuencia de actualizaciones
                // y mejorar el rendimiento durante el desplazamiento rápido
                Qt.callLater(updateSecondaryActiveLine)
            }
        }

        // Función para actualizar la línea activa secundaria basada en la posición del scrollbar
        function updateSecondaryActiveLine() {
            // Calcular qué línea está en el centro de la vista
            var centerY = contentY + height / 2;
            var closestItem = null;
            var closestDistance = Number.MAX_VALUE;
            var closestIndex = -1;

            // Primera pasada: encontrar la línea más cercana al centro
            for (var j = 0; j < contentItem.children.length; j++) {
                var child = contentItem.children[j];
                if (!child || child.y === undefined) continue;

                // Calcular la distancia al centro
                var itemCenterY = child.y + child.height / 2;
                var distance = Math.abs(centerY - itemCenterY);

                if (distance < closestDistance) {
                    closestDistance = distance;
                    closestItem = child;
                    // Determinar el índice de la línea más cercana
                    // Intentar obtener el índice directamente del elemento
                    if (child.index !== undefined) {
                        closestIndex = child.index;
                    }
                    // Si no está disponible, intentar obtenerlo de la vista
                    else if (child.ListView && child.ListView.view && child.ListView.view.model) {
                        closestIndex = child.ListView.view.indexAt(child.x + child.width/2, child.y + child.height/2);
                    }
                }
            }

            // Segunda pasada: actualizar todas las líneas con su distancia a la línea secundaria
            for (var i = 0; i < contentItem.children.length; i++) {
                var item = contentItem.children[i];
                if (!item || item.updateSecondaryDistance === undefined) continue;

                // Resetear el estado de línea secundaria activa
                item.isSecondaryActive = false;

                // Actualizar la distancia a la línea secundaria
                item.updateSecondaryDistance(closestIndex);
            }

            // Marcar la línea más cercana como secundaria activa solo si la barra de desplazamiento está activa
            if (closestItem && closestItem.isSecondaryActive !== undefined && isScrollbarActive) {
                // Calcular la distancia entre la línea secundaria y la línea principal
                var distanceToMainLine = Math.abs(closestIndex - root.currentIndex);

                // Siempre activar la línea secundaria cuando la barra de desplazamiento está activa
                closestItem.isSecondaryActive = true;

                // Marcar si la línea secundaria está cerca de la línea principal
                closestItem.isNearPrimary = (distanceToMainLine <= 8);
            }
        }

        // Añadir espacio antes y después para el efecto de desvanecimiento
        // Ajustado dinámicamente según el estado de la carátula
        header: Item {
            width: lyricsView.width
            height: lyricsView.height * (root.albumCoverExpanded ? 0.03 : 0.05)

            // Animación suave para la transición de altura
            Behavior on height {
                NumberAnimation {
                    duration: 350
                    easing.type: Easing.OutCubic
                }
            }
        }

        footer: Item {
            width: lyricsView.width
            height: lyricsView.height * (root.albumCoverExpanded ? 0.03 : 0.05)

            // Animación suave para la transición de altura
            Behavior on height {
                NumberAnimation {
                    duration: 350
                    easing.type: Easing.OutCubic
                }
            }
        }

        // Configuración para centrar la línea activa según el estado de la carátula
        preferredHighlightBegin: height / 2 - (root.albumCoverExpanded ? 14 : 18)
        preferredHighlightEnd: height / 2 + (root.albumCoverExpanded ? 14 : 18)

        // Animación suave para la transición de los puntos de resaltado
        Behavior on preferredHighlightBegin {
            NumberAnimation {
                duration: 350
                easing.type: Easing.OutCubic
            }
        }

        Behavior on preferredHighlightEnd {
            NumberAnimation {
                duration: 350
                easing.type: Easing.OutCubic
            }
        }

        highlightRangeMode: ListView.ApplyRange  // Modo más flexible para animaciones más suaves
        highlightFollowsCurrentItem: true

        // Inicialización suave del componente
        Component.onCompleted: {
            // Establecer el índice actual sin forzar posicionamiento inmediato
            currentIndex = root.currentIndex;
        }

        // Animación suave al cambiar de línea (restaurada)
        highlightMoveDuration: 600
        highlightMoveVelocity: -1
        highlightResizeDuration: 2000
        highlightResizeVelocity: -1

        // Actualizar el índice actual
        onModelChanged: {
            if (root.currentIndex >= 0 && root.currentIndex < count) {
                currentIndex = root.currentIndex;
            }
        }

        // Actualizar cuando cambia el índice actual
        onCurrentIndexChanged: {
            if (currentIndex !== root.currentIndex) {
                currentIndex = root.currentIndex;
                // Permitir que la animación se encargue del posicionamiento
            }
        }

        // Actualizar cuando cambia el índice actual desde fuera
        Connections {
            target: root
            function onCurrentIndexChanged() {
                if (lyricsView.currentIndex !== root.currentIndex) {
                    lyricsView.currentIndex = root.currentIndex;
                    // Permitir que la animación se encargue del posicionamiento
                }
            }
        }

        // Función para detener la animación de desplazamiento
        function stopScrollAnimation() {
            if (scrollAnimation.running) {
                scrollAnimation.stop();
            }
        }

        // Función para redirigir suavemente la línea secundaria a la línea principal
        function redirectSecondaryLine() {
            // Calcular la posición Y de la línea principal
            var targetIndex = root.currentIndex;
            var targetItem = null;

            // Buscar el elemento visual correspondiente a la línea principal
            for (var i = 0; i < contentItem.children.length; i++) {
                var item = contentItem.children[i];
                if (!item || item.isCurrent === undefined) continue;

                if (item.isCurrent) {
                    targetItem = item;
                    break;
                }
            }

            // Si encontramos el elemento, animar el desplazamiento hacia él
            if (targetItem) {
                // Calcular la posición Y del centro del elemento
                var targetY = targetItem.y + (targetItem.height / 2) - (height / 2);

                // Animar el desplazamiento
                scrollAnimation.to = Math.max(0, Math.min(targetY, contentHeight - height));
                scrollAnimation.start();
            }
        }

        // Animación para el desplazamiento suave
        NumberAnimation {
            id: scrollAnimation
            target: lyricsView
            property: "contentY"
            duration: 800
            easing.type: Easing.OutCubic

            // Actualizar la línea secundaria al finalizar
            onFinished: {
                lyricsView.updateSecondaryActiveLine();
            }
        }
    }

    // Función para cargar letras desde un archivo de texto
    function loadLyricsFromText(text) {
        var lines = text.split('\n');
        var cleanLines = [];

        // Filtrar líneas vacías y procesar
        for (var i = 0; i < lines.length; i++) {
            var line = lines[i].trim();
            if (line.length > 0) {
                cleanLines.push(line);
            }
        }

        // Actualizar el modelo
        root.lyricsModel = cleanLines;
        root.currentIndex = 0;
        root.progress = 0.0;

        // Asegurar que la vista se actualice correctamente
        if (lyricsView) {
            lyricsView.positionViewAtIndex(0, ListView.Center);
            lyricsView.updateSecondaryActiveLine();
        }

        return cleanLines.length;
    }

    // Función para actualizar el progreso de la línea actual
    function updateProgress(progress) {
        root.progress = Math.max(0.0, Math.min(1.0, progress));
    }

    // Función para avanzar a la siguiente línea
    function nextLine() {
        if (root.currentIndex < root.lyricsModel.length - 1) {
            root.currentIndex++;
            root.progress = 0.0;

            // Permitir que la animación se encargue del posicionamiento
            // para una transición más suave

            return true;
        }
        return false;
    }

    // Función para retroceder a la línea anterior
    function previousLine() {
        if (root.currentIndex > 0) {
            root.currentIndex--;
            root.progress = 0.0;

            // Permitir que la animación se encargue del posicionamiento
            // para una transición más suave

            return true;
        }
        return false;
    }
}
